import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'simple_home_screen.dart';

/// 简化的测试应用
/// 专门用于测试权限功能
class SimpleTestApp extends StatelessWidget {
  const SimpleTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Hera 权限测试',
      theme: ThemeData(
        primarySwatch: Colors.blue,
        visualDensity: VisualDensity.adaptivePlatformDensity,
      ),
      home: const SimpleHomeScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// 简化的main函数
void runSimpleTestApp() {
  runApp(
    const ProviderScope(
      child: SimpleTestApp(),
    ),
  );
} 