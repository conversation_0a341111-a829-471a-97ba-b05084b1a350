import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../core/services/clean_permission_manager.dart';

/// 清洁的图片选择器
/// 使用新的权限管理器，确保权限申请流程简洁有效
class CleanImagePicker extends StatefulWidget {
  final Function(List<String>)? onImagesSelected;
  final int maxImages;
  final bool allowMultiple;
  final String buttonText;
  final Color? buttonColor;
  final IconData? buttonIcon;

  const CleanImagePicker({
    super.key,
    this.onImagesSelected,
    this.maxImages = 1,
    this.allowMultiple = false,
    this.buttonText = '选择照片',
    this.buttonColor,
    this.buttonIcon,
  });

  @override
  State<CleanImagePicker> createState() => _CleanImagePickerState();
}

class _CleanImagePickerState extends State<CleanImagePicker> {
  final ImagePicker _picker = ImagePicker();
  final CleanPermissionManager _permissionManager = CleanPermissionManager();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 主要选择按钮
        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton.icon(
            onPressed: _isLoading ? null : _showImageSourceDialog,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : Icon(widget.buttonIcon ?? Icons.photo_library),
            label: Text(_isLoading ? '处理中...' : widget.buttonText),
            style: ElevatedButton.styleFrom(
              backgroundColor: widget.buttonColor ?? Colors.blue,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),
        const SizedBox(height: 8),
        // 提示文本
        Text(
          widget.allowMultiple 
              ? '最多可选择${widget.maxImages}张照片'
              : '选择一张照片',
          style: TextStyle(
            fontSize: 12,
            color: Colors.grey.shade600,
          ),
        ),
      ],
    );
  }

  /// 显示图片来源选择对话框
  Future<void> _showImageSourceDialog() async {
    final result = await showModalBottomSheet<String>(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (BuildContext context) {
        return Container(
          padding: const EdgeInsets.all(20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 标题
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey.shade300,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),
              const SizedBox(height: 20),
              Text(
                '选择图片来源',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey.shade800,
                ),
              ),
              const SizedBox(height: 20),
              
              // 相册选项
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.photo_library, color: Colors.blue),
                ),
                title: const Text('从相册选择'),
                subtitle: const Text('选择已有的照片'),
                onTap: () => Navigator.of(context).pop('gallery'),
              ),
              
              // 相机选项
              ListTile(
                leading: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.camera_alt, color: Colors.green),
                ),
                title: const Text('拍照'),
                subtitle: const Text('使用相机拍摄新照片'),
                onTap: () => Navigator.of(context).pop('camera'),
              ),
              
              const SizedBox(height: 10),
              
              // 取消按钮
              SizedBox(
                width: double.infinity,
                child: TextButton(
                  onPressed: () => Navigator.of(context).pop(),
                  child: const Text('取消'),
                ),
              ),
            ],
          ),
        );
      },
    );

    if (result != null) {
      if (result == 'gallery') {
        await _pickFromGallery();
      } else if (result == 'camera') {
        await _pickFromCamera();
      }
    }
  }

  /// 从相册选择照片
  Future<void> _pickFromGallery() async {
    setState(() => _isLoading = true);

    try {
      // 1. 请求相册权限
      final hasPermission = await _permissionManager.requestPhotosPermission(context);
      
      if (!hasPermission) {
        print('❌ 相册权限获取失败');
        return;
      }

      // 2. 选择照片
      if (widget.allowMultiple) {
        final images = await _picker.pickMultiImage(
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );
        
        if (images.isNotEmpty) {
          // 限制选择数量
          final selectedImages = images.take(widget.maxImages).toList();
          final imagePaths = selectedImages.map((image) => image.path).toList();
          
          widget.onImagesSelected?.call(imagePaths);
          _showSuccessSnackBar('成功选择${selectedImages.length}张照片');
        }
      } else {
        final image = await _picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );
        
        if (image != null) {
          widget.onImagesSelected?.call([image.path]);
          _showSuccessSnackBar('照片选择成功');
        }
      }
    } catch (e) {
      print('💥 选择照片时发生错误: $e');
      _showErrorSnackBar('选择照片失败，请重试');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 从相机拍照
  Future<void> _pickFromCamera() async {
    setState(() => _isLoading = true);

    try {
      // 1. 请求相机权限
      final hasPermission = await _permissionManager.requestCameraPermission(context);
      
      if (!hasPermission) {
        print('❌ 相机权限获取失败');
        return;
      }

      // 2. 拍照
      final image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      
      if (image != null) {
        widget.onImagesSelected?.call([image.path]);
        _showSuccessSnackBar('拍照成功');
      }
    } catch (e) {
      print('💥 拍照时发生错误: $e');
      _showErrorSnackBar('拍照失败，请重试');
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    if (!mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
