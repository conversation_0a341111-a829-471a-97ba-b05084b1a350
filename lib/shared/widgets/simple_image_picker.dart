import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../core/services/permission_reset_service.dart';

/// 简单图片选择器组件
class SimpleImagePicker extends StatefulWidget {
  final Function(List<String> imagePaths)? onImagesSelected;
  final int maxImages;
  final bool allowMultiple;
  final String buttonText;

  const SimpleImagePicker({
    super.key,
    this.onImagesSelected,
    this.maxImages = 3,
    this.allowMultiple = true,
    this.buttonText = '选择照片',
  });

  @override
  State<SimpleImagePicker> createState() => _SimpleImagePickerState();
}

class _SimpleImagePickerState extends State<SimpleImagePicker> {
  final ImagePicker _picker = ImagePicker();
  final PermissionResetService _permissionService = PermissionResetService();
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ElevatedButton.icon(
          onPressed: _isLoading ? null : () => _showImageSourceDialog(),
          icon: _isLoading 
              ? const SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Icon(Icons.photo_library),
          label: Text(_isLoading ? '处理中...' : widget.buttonText),
        ),
      ],
    );
  }

  /// 显示图片来源选择对话框
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      builder: (BuildContext context) {
        return SafeArea(
          child: Wrap(
            children: [
              ListTile(
                leading: const Icon(Icons.photo_library),
                title: const Text('从相册选择'),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromGallery();
                },
              ),
              ListTile(
                leading: const Icon(Icons.photo_camera),
                title: const Text('拍照'),
                onTap: () {
                  Navigator.pop(context);
                  _pickFromCamera();
                },
              ),
              ListTile(
                leading: const Icon(Icons.cancel),
                title: const Text('取消'),
                onTap: () {
                  Navigator.pop(context);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  /// 从相册选择图片
  Future<void> _pickFromGallery() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 使用强制重置权限服务
      bool hasPermission = await _permissionService.forceResetPhotosPermission(context);

      if (!hasPermission) {
        print('相册权限获取失败');
        if (mounted) {
          _showSnackBar('需要相册权限才能选择照片', Colors.red);
        }
        return;
      }

      // 选择图片
      if (widget.allowMultiple) {
        final List<XFile> images = await _picker.pickMultiImage(
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );
        
        if (images.isNotEmpty) {
          List<String> imagePaths = images.map((image) => image.path).toList();
          
          // 限制数量
          if (imagePaths.length > widget.maxImages) {
            imagePaths = imagePaths.take(widget.maxImages).toList();
            if (mounted) {
              _showSnackBar('最多只能选择${widget.maxImages}张图片', Colors.orange);
            }
          }
          
          widget.onImagesSelected?.call(imagePaths);
          if (mounted) {
            _showSnackBar('成功选择${imagePaths.length}张图片', Colors.green);
          }
        }
      } else {
        final XFile? image = await _picker.pickImage(
          source: ImageSource.gallery,
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );
        
        if (image != null) {
          widget.onImagesSelected?.call([image.path]);
          if (mounted) {
            _showSnackBar('成功选择图片', Colors.green);
          }
        }
      }
    } catch (e) {
      print('从相册选择图片时发生错误: $e');
      if (mounted) {
        _showSnackBar('选择图片失败，请重试', Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 从相机拍照
  Future<void> _pickFromCamera() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 使用强制重置相机权限服务
      bool hasPermission = await _permissionService.forceResetCameraPermission(context);

      if (!hasPermission) {
        print('相机权限获取失败');
        if (mounted) {
          _showSnackBar('需要相机权限才能拍照', Colors.red);
        }
        return;
      }

      // 拍照
      final XFile? image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );
      
      if (image != null) {
        widget.onImagesSelected?.call([image.path]);
        if (mounted) {
          _showSnackBar('拍照成功', Colors.green);
        }
      }
    } catch (e) {
      print('拍照时发生错误: $e');
      if (mounted) {
        _showSnackBar('拍照失败，请重试', Colors.red);
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 显示提示消息
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }
} 