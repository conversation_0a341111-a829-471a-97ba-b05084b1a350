import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/utils/log_service.dart';
import '../entities/image_upload.dart';
import '../repositories/image_upload_repository.dart';

/// 上传图片用例
class UploadImagesUseCase {
  final ImageUploadRepository _repository;
  final LogService _logger;

  UploadImagesUseCase(this._repository, this._logger);

  /// 执行图片上传
  Future<Either<Failure, List<ImageUpload>>> call(UploadImagesParams params) async {
    _logger.i('开始上传图片: ${params.imagePaths.length} 张');
    
    try {
      // 1. 验证所有图片
      _logger.d('验证图片文件...');
      for (final imagePath in params.imagePaths) {
        final validationResult = await _repository.validateImage(imagePath);
        if (validationResult.isLeft()) {
          _logger.e('图片验证失败: $imagePath');
          return validationResult.fold(
            (failure) => Left(failure),
            (_) => throw Exception('Unexpected validation result'),
          );
        }
      }

      // 2. 批量上传图片
      _logger.d('开始批量上传图片...');
      final uploadResult = await _repository.uploadImages(
        params.imagePaths,
        onProgress: (progress) {
          _logger.d('上传进度: $progress%');
          params.onProgress?.call(progress);
        },
      );

      return uploadResult.fold(
        (failure) {
          _logger.e('图片上传失败: ${failure.message}');
          return Left(failure);
        },
        (uploads) {
          _logger.i('图片上传成功: ${uploads.length} 张');
          return Right(uploads);
        },
      );
    } catch (e, stackTrace) {
      _logger.e('上传图片时发生异常', e, stackTrace);
      return Left(ServerFailure('上传图片时发生异常: $e'));
    }
  }
}

/// 上传图片参数
class UploadImagesParams {
  final List<String> imagePaths;
  final Function(int progress)? onProgress;

  UploadImagesParams({
    required this.imagePaths,
    this.onProgress,
  });
}
