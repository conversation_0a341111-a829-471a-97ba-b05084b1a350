import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/utils/log_service.dart';
import '../entities/generation_result.dart';
import '../repositories/ai_generation_repository.dart';

/// 获取风格模板用例
class GetStyleTemplatesUseCase {
  final AiGenerationRepository _repository;
  final LogService _logger;

  GetStyleTemplatesUseCase(this._repository, this._logger);

  /// 执行获取风格模板
  Future<Either<Failure, List<StyleTemplate>>> call() async {
    _logger.i('开始获取风格模板列表');
    
    try {
      final result = await _repository.getStyleTemplates();
      
      return result.fold(
        (failure) {
          _logger.e('获取风格模板失败: ${failure.message}');
          return Left(failure);
        },
        (templates) {
          _logger.i('成功获取风格模板: ${templates.length} 个');
          _logger.d('模板列表: ${templates.map((t) => '${t.id}:${t.name}').join(', ')}');
          return Right(templates);
        },
      );
    } catch (e, stackTrace) {
      _logger.e('获取风格模板时发生异常', e, stackTrace);
      return Left(ServerFailure('获取风格模板时发生异常: $e'));
    }
  }
}

/// 获取单个风格模板用例
class GetStyleTemplateUseCase {
  final AiGenerationRepository _repository;
  final LogService _logger;

  GetStyleTemplateUseCase(this._repository, this._logger);

  /// 执行获取单个风格模板
  Future<Either<Failure, StyleTemplate>> call(String templateId) async {
    _logger.i('获取风格模板: $templateId');
    
    try {
      final result = await _repository.getStyleTemplate(templateId);
      
      return result.fold(
        (failure) {
          _logger.e('获取风格模板失败: ${failure.message}');
          return Left(failure);
        },
        (template) {
          _logger.i('成功获取风格模板: ${template.name}');
          return Right(template);
        },
      );
    } catch (e, stackTrace) {
      _logger.e('获取风格模板时发生异常', e, stackTrace);
      return Left(ServerFailure('获取风格模板时发生异常: $e'));
    }
  }
}
