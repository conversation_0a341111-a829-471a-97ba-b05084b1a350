import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/utils/log_service.dart';
import '../entities/generation_request.dart';
import '../entities/generation_result.dart';
import '../repositories/ai_generation_repository.dart';

/// 生成婚纱照用例
class GenerateWeddingPhotoUseCase {
  final AiGenerationRepository _repository;
  final LogService _logger;

  GenerateWeddingPhotoUseCase(this._repository, this._logger);

  /// 执行婚纱照生成
  Future<Either<Failure, GenerationResult>> call(GenerateWeddingPhotoParams params) async {
    _logger.i('开始生成婚纱照: 风格=${params.styleTemplateId}');
    
    try {
      // 1. 提交生成请求
      _logger.d('提交AI生成请求...');
      final requestResult = await _repository.submitGenerationRequest(
        imagePaths: params.imagePaths,
        styleTemplateId: params.styleTemplateId,
        customParameters: params.customParameters,
      );

      if (requestResult.isLeft()) {
        return requestResult.fold(
          (failure) {
            _logger.e('提交生成请求失败: ${failure.message}');
            return Left(failure);
          },
          (_) => throw Exception('Unexpected request result'),
        );
      }

      final request = requestResult.getOrElse(() => throw Exception('No request'));
      _logger.i('生成请求已提交: ${request.id}');

      // 2. 轮询生成状态
      _logger.d('开始轮询生成状态...');
      GenerationRequest currentRequest = request;
      
      while (currentRequest.status == GenerationStatus.pending ||
             currentRequest.status == GenerationStatus.uploading ||
             currentRequest.status == GenerationStatus.processing) {
        
        // 等待一段时间后再次查询
        await Future.delayed(const Duration(seconds: 3));
        
        final statusResult = await _repository.getGenerationStatus(currentRequest.id);
        if (statusResult.isLeft()) {
          return statusResult.fold(
            (failure) {
              _logger.e('查询生成状态失败: ${failure.message}');
              return Left(failure);
            },
            (_) => throw Exception('Unexpected status result'),
          );
        }

        currentRequest = statusResult.getOrElse(() => throw Exception('No status'));
        _logger.d('生成状态: ${currentRequest.status}');
        
        // 通知进度回调
        params.onProgress?.call(currentRequest);

        // 检查是否失败或取消
        if (currentRequest.status == GenerationStatus.failed) {
          _logger.e('生成失败: ${currentRequest.errorMessage}');
          return Left(ServerFailure(currentRequest.errorMessage ?? '生成失败'));
        }
        
        if (currentRequest.status == GenerationStatus.cancelled) {
          _logger.w('生成已取消');
          return Left(CancelledFailure('生成已取消'));
        }
      }

      // 3. 获取生成结果
      if (currentRequest.status == GenerationStatus.completed) {
        _logger.d('获取生成结果...');
        final resultResult = await _repository.getGenerationResult(currentRequest.id);
        
        return resultResult.fold(
          (failure) {
            _logger.e('获取生成结果失败: ${failure.message}');
            return Left(failure);
          },
          (result) {
            _logger.i('婚纱照生成成功: ${result.imageUrls.length} 张图片');
            return Right(result);
          },
        );
      }

      _logger.e('未知的生成状态: ${currentRequest.status}');
      return Left(ServerFailure('未知的生成状态'));

    } catch (e, stackTrace) {
      _logger.e('生成婚纱照时发生异常', e, stackTrace);
      return Left(ServerFailure('生成婚纱照时发生异常: $e'));
    }
  }
}

/// 生成婚纱照参数
class GenerateWeddingPhotoParams {
  final List<String> imagePaths;
  final String styleTemplateId;
  final Map<String, dynamic> customParameters;
  final Function(GenerationRequest request)? onProgress;

  GenerateWeddingPhotoParams({
    required this.imagePaths,
    required this.styleTemplateId,
    required this.customParameters,
    this.onProgress,
  });
}
