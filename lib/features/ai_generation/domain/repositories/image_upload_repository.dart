import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/image_upload.dart';

/// 图片上传仓库接口
abstract class ImageUploadRepository {
  /// 验证图片文件
  Future<Either<Failure, ImageMetadata>> validateImage(String imagePath);

  /// 上传单张图片
  Future<Either<Failure, ImageUpload>> uploadImage(
    String imagePath, {
    Function(int progress)? onProgress,
  });

  /// 批量上传图片
  Future<Either<Failure, List<ImageUpload>>> uploadImages(
    List<String> imagePaths, {
    Function(int totalProgress)? onProgress,
  });

  /// 获取上传状态
  Future<Either<Failure, ImageUpload>> getUploadStatus(String uploadId);

  /// 取消上传
  Future<Either<Failure, void>> cancelUpload(String uploadId);

  /// 删除已上传的图片
  Future<Either<Failure, void>> deleteUploadedImage(String uploadId);

  /// 压缩图片
  Future<Either<Failure, String>> compressImage(
    String imagePath, {
    int quality = 85,
    int maxWidth = 1024,
    int maxHeight = 1024,
  });

  /// 检测人脸
  Future<Either<Failure, List<FaceRegion>>> detectFaces(String imagePath);

  /// 评估图片质量
  Future<Either<Failure, int>> assessImageQuality(String imagePath);
}
