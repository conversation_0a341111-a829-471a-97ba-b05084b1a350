import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../entities/generation_request.dart';
import '../entities/generation_result.dart';

/// AI生成仓库接口
abstract class AiGenerationRepository {
  /// 获取可用的风格模板列表
  Future<Either<Failure, List<StyleTemplate>>> getStyleTemplates();

  /// 根据ID获取风格模板
  Future<Either<Failure, StyleTemplate>> getStyleTemplate(String templateId);

  /// 提交AI生成请求
  Future<Either<Failure, GenerationRequest>> submitGenerationRequest({
    required List<String> imagePaths,
    required String styleTemplateId,
    required Map<String, dynamic> customParameters,
  });

  /// 获取生成状态
  Future<Either<Failure, GenerationRequest>> getGenerationStatus(String requestId);

  /// 获取生成结果
  Future<Either<Failure, GenerationResult>> getGenerationResult(String requestId);

  /// 取消生成请求
  Future<Either<Failure, void>> cancelGenerationRequest(String requestId);

  /// 获取用户的生成历史
  Future<Either<Failure, List<GenerationResult>>> getGenerationHistory({
    int page = 1,
    int limit = 20,
  });

  /// 删除生成结果
  Future<Either<Failure, void>> deleteGenerationResult(String resultId);

  /// 保存生成结果到本地
  Future<Either<Failure, String>> saveResultToLocal(String resultId, String imageUrl);
}
