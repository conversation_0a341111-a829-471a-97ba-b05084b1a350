import 'package:freezed_annotation/freezed_annotation.dart';

part 'generation_result.freezed.dart';
part 'generation_result.g.dart';

/// AI生成结果实体
@freezed
class GenerationResult with _$GenerationResult {
  const factory GenerationResult({
    /// 结果ID
    required String id,
    /// 关联的请求ID
    required String requestId,
    /// 生成的图片URL列表
    required List<String> imageUrls,
    /// 生成的图片本地路径列表（用于缓存）
    List<String>? localImagePaths,
    /// 使用的风格模板信息
    required StyleTemplate styleTemplate,
    /// 生成参数
    required Map<String, dynamic> generationParameters,
    /// 生成完成时间
    required DateTime completedAt,
    /// 图片质量评分 (0-100)
    required int qualityScore,
    /// 人脸相似度评分 (0-100)
    required int similarityScore,
    /// 生成耗时（秒）
    required int processingTimeSeconds,
  }) = _GenerationResult;

  factory GenerationResult.fromJson(Map<String, dynamic> json) =>
      _$GenerationResultFromJson(json);
}

/// 风格模板实体
@freezed
class StyleTemplate with _$StyleTemplate {
  const factory StyleTemplate({
    /// 模板ID
    required String id,
    /// 模板名称
    required String name,
    /// 模板分类
    required String category,
    /// 模板描述
    required String description,
    /// 预览图片URL
    required String previewImageUrl,
    /// AI提示词
    required String prompt,
    /// 负面提示词
    required String negativePrompt,
    /// 生成参数
    required StyleParameters parameters,
    /// 是否为高级模板
    @Default(false) bool isPremium,
  }) = _StyleTemplate;

  factory StyleTemplate.fromJson(Map<String, dynamic> json) =>
      _$StyleTemplateFromJson(json);
}

/// 风格参数
@freezed
class StyleParameters with _$StyleParameters {
  const factory StyleParameters({
    /// 生成步数
    @Default(30) int steps,
    /// 引导强度
    @Default(7.5) double guidanceScale,
    /// 强度
    @Default(0.8) double strength,
    /// 种子值（可选）
    int? seed,
    /// 输出分辨率
    @Default('1024x1024') String resolution,
  }) = _StyleParameters;

  factory StyleParameters.fromJson(Map<String, dynamic> json) =>
      _$StyleParametersFromJson(json);
}
