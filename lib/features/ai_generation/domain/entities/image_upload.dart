import 'package:freezed_annotation/freezed_annotation.dart';

part 'image_upload.freezed.dart';
part 'image_upload.g.dart';

/// 图片上传实体
@freezed
class ImageUpload with _$ImageUpload {
  const factory ImageUpload({
    /// 上传ID
    required String id,
    /// 本地文件路径
    required String localPath,
    /// 远程URL（上传成功后）
    String? remoteUrl,
    /// 文件名
    required String fileName,
    /// 文件大小（字节）
    required int fileSize,
    /// 文件类型
    required String mimeType,
    /// 上传状态
    required UploadStatus status,
    /// 上传进度 (0-100)
    @Default(0) int progress,
    /// 图片元数据
    ImageMetadata? metadata,
    /// 错误信息
    String? errorMessage,
    /// 创建时间
    required DateTime createdAt,
    /// 完成时间
    DateTime? completedAt,
  }) = _ImageUpload;

  factory ImageUpload.fromJson(Map<String, dynamic> json) =>
      _$ImageUploadFromJson(json);
}

/// 上传状态枚举
enum UploadStatus {
  /// 等待上传
  pending,
  /// 验证中
  validating,
  /// 上传中
  uploading,
  /// 已完成
  completed,
  /// 失败
  failed,
  /// 已取消
  cancelled,
}

/// 图片元数据
@freezed
class ImageMetadata with _$ImageMetadata {
  const factory ImageMetadata({
    /// 图片宽度
    required int width,
    /// 图片高度
    required int height,
    /// 是否检测到人脸
    required bool hasFace,
    /// 人脸数量
    @Default(0) int faceCount,
    /// 图片质量评分 (0-100)
    required int qualityScore,
    /// 是否模糊
    @Default(false) bool isBlurry,
    /// 光线充足度评分 (0-100)
    required int lightingScore,
    /// 人脸区域坐标 (x, y, width, height)
    List<FaceRegion>? faceRegions,
  }) = _ImageMetadata;

  factory ImageMetadata.fromJson(Map<String, dynamic> json) =>
      _$ImageMetadataFromJson(json);
}

/// 人脸区域
@freezed
class FaceRegion with _$FaceRegion {
  const factory FaceRegion({
    /// X坐标
    required double x,
    /// Y坐标
    required double y,
    /// 宽度
    required double width,
    /// 高度
    required double height,
    /// 置信度 (0-1)
    required double confidence,
  }) = _FaceRegion;

  factory FaceRegion.fromJson(Map<String, dynamic> json) =>
      _$FaceRegionFromJson(json);
}
