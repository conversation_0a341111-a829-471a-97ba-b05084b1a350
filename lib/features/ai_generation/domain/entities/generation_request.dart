import 'package:freezed_annotation/freezed_annotation.dart';

part 'generation_request.freezed.dart';
part 'generation_request.g.dart';

/// AI生成请求实体
@freezed
class GenerationRequest with _$GenerationRequest {
  const factory GenerationRequest({
    /// 请求ID
    required String id,
    /// 用户上传的图片路径列表
    required List<String> imagePaths,
    /// 选择的风格模板ID
    required String styleTemplateId,
    /// 自定义参数
    required Map<String, dynamic> customParameters,
    /// 请求创建时间
    required DateTime createdAt,
    /// 请求状态
    required GenerationStatus status,
    /// 错误信息（如果有）
    String? errorMessage,
  }) = _GenerationRequest;

  factory GenerationRequest.fromJson(Map<String, dynamic> json) =>
      _$GenerationRequestFromJson(json);
}

/// 生成状态枚举
enum GenerationStatus {
  /// 等待中
  pending,
  /// 上传中
  uploading,
  /// 处理中
  processing,
  /// 已完成
  completed,
  /// 失败
  failed,
  /// 已取消
  cancelled,
}
