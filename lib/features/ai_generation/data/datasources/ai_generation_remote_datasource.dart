import '../../../../core/network/dio_client.dart';
import '../../../../core/utils/log_service.dart';
import '../models/generation_request_model.dart';
import '../models/generation_result_model.dart';

/// AI生成远程数据源接口
abstract class AiGenerationRemoteDataSource {
  /// 获取风格模板列表
  Future<List<StyleTemplateModel>> getStyleTemplates();

  /// 获取单个风格模板
  Future<StyleTemplateModel> getStyleTemplate(String templateId);

  /// 提交生成请求
  Future<GenerationRequestModel> submitGenerationRequest({
    required List<String> imageUrls,
    required String styleTemplateId,
    required Map<String, dynamic> customParameters,
  });

  /// 获取生成状态
  Future<GenerationRequestModel> getGenerationStatus(String requestId);

  /// 获取生成结果
  Future<GenerationResultModel> getGenerationResult(String requestId);

  /// 取消生成请求
  Future<void> cancelGenerationRequest(String requestId);

  /// 获取生成历史
  Future<List<GenerationResultModel>> getGenerationHistory({
    int page = 1,
    int limit = 20,
  });

  /// 删除生成结果
  Future<void> deleteGenerationResult(String resultId);
}

/// AI生成远程数据源实现
class AiGenerationRemoteDataSourceImpl implements AiGenerationRemoteDataSource {
  final DioClient _dioClient;
  final LogService _logger;

  AiGenerationRemoteDataSourceImpl(this._dioClient, this._logger);

  @override
  Future<List<StyleTemplateModel>> getStyleTemplates() async {
    _logger.d('API调用: 获取风格模板列表');
    
    try {
      final responseData = await _dioClient.get('/ai-generation/style-templates');
      _logger.d('API响应: 风格模板列表 - $responseData');

      final List<dynamic> data = responseData['data'] ?? [];
      return data.map((json) => StyleTemplateModel.fromJson(json)).toList();
    } catch (e, stackTrace) {
      _logger.e('获取风格模板列表API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<StyleTemplateModel> getStyleTemplate(String templateId) async {
    _logger.d('API调用: 获取风格模板 - $templateId');
    
    try {
      final responseData = await _dioClient.get('/ai-generation/style-templates/$templateId');
      _logger.d('API响应: 风格模板详情 - $responseData');

      return StyleTemplateModel.fromJson(responseData['data']);
    } catch (e, stackTrace) {
      _logger.e('获取风格模板API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<GenerationRequestModel> submitGenerationRequest({
    required List<String> imageUrls,
    required String styleTemplateId,
    required Map<String, dynamic> customParameters,
  }) async {
    _logger.d('API调用: 提交生成请求');
    _logger.d('请求参数: 图片=${imageUrls.length}张, 风格=$styleTemplateId');
    
    try {
      final requestData = {
        'image_urls': imageUrls,
        'style_template_id': styleTemplateId,
        'custom_parameters': customParameters,
      };
      
      final responseData = await _dioClient.post(
        '/ai-generation/generate',
        data: requestData,
      );

      _logger.d('API响应: 生成请求已提交 - $responseData');
      return GenerationRequestModel.fromJson(responseData['data']);
    } catch (e, stackTrace) {
      _logger.e('提交生成请求API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<GenerationRequestModel> getGenerationStatus(String requestId) async {
    _logger.d('API调用: 获取生成状态 - $requestId');
    
    try {
      final responseData = await _dioClient.get('/ai-generation/requests/$requestId/status');
      _logger.d('API响应: 生成状态 - $responseData');

      return GenerationRequestModel.fromJson(responseData['data']);
    } catch (e, stackTrace) {
      _logger.e('获取生成状态API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<GenerationResultModel> getGenerationResult(String requestId) async {
    _logger.d('API调用: 获取生成结果 - $requestId');
    
    try {
      final responseData = await _dioClient.get('/ai-generation/requests/$requestId/result');
      _logger.d('API响应: 生成结果 - $responseData');

      return GenerationResultModel.fromJson(responseData['data']);
    } catch (e, stackTrace) {
      _logger.e('获取生成结果API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> cancelGenerationRequest(String requestId) async {
    _logger.d('API调用: 取消生成请求 - $requestId');
    
    try {
      await _dioClient.post('/ai-generation/requests/$requestId/cancel');
      _logger.d('API响应: 生成请求已取消');
    } catch (e, stackTrace) {
      _logger.e('取消生成请求API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<GenerationResultModel>> getGenerationHistory({
    int page = 1,
    int limit = 20,
  }) async {
    _logger.d('API调用: 获取生成历史 - page=$page, limit=$limit');
    
    try {
      final responseData = await _dioClient.get(
        '/ai-generation/history',
        queryParameters: {'page': page, 'limit': limit},
      );
      _logger.d('API响应: 生成历史 - $responseData');

      final List<dynamic> data = responseData['data'] ?? [];
      return data.map((json) => GenerationResultModel.fromJson(json)).toList();
    } catch (e, stackTrace) {
      _logger.e('获取生成历史API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteGenerationResult(String resultId) async {
    _logger.d('API调用: 删除生成结果 - $resultId');
    
    try {
      await _dioClient.delete('/ai-generation/results/$resultId');
      _logger.d('API响应: 生成结果已删除');
    } catch (e, stackTrace) {
      _logger.e('删除生成结果API调用失败', e, stackTrace);
      rethrow;
    }
  }
}
