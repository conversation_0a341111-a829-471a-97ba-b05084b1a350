import 'dart:io';
import 'package:dio/dio.dart';
import 'package:http_parser/http_parser.dart';
import '../../../../core/network/dio_client.dart';
import '../../../../core/utils/log_service.dart';
import '../models/image_upload_model.dart';

/// 图片上传远程数据源接口
abstract class ImageUploadRemoteDataSource {
  /// 上传单张图片
  Future<ImageUploadModel> uploadImage(
    String imagePath, {
    Function(int progress)? onProgress,
  });

  /// 批量上传图片
  Future<List<ImageUploadModel>> uploadImages(
    List<String> imagePaths, {
    Function(int totalProgress)? onProgress,
  });

  /// 获取上传状态
  Future<ImageUploadModel> getUploadStatus(String uploadId);

  /// 取消上传
  Future<void> cancelUpload(String uploadId);

  /// 删除已上传的图片
  Future<void> deleteUploadedImage(String uploadId);
}

/// 图片上传远程数据源实现
class ImageUploadRemoteDataSourceImpl implements ImageUploadRemoteDataSource {
  final DioClient _dioClient;
  final LogService _logger;

  ImageUploadRemoteDataSourceImpl(this._dioClient, this._logger);

  @override
  Future<ImageUploadModel> uploadImage(
    String imagePath, {
    Function(int progress)? onProgress,
  }) async {
    _logger.d('API调用: 上传图片 - $imagePath');
    
    try {
      final file = File(imagePath);
      final fileName = file.path.split('/').last;
      final fileSize = await file.length();
      
      _logger.d('图片信息: 文件名=$fileName, 大小=${fileSize}字节');

      // 创建FormData
      final formData = FormData.fromMap({
        'image': await MultipartFile.fromFile(
          imagePath,
          filename: fileName,
          contentType: MediaType('image', _getImageType(fileName)),
        ),
      });

      // 上传图片
      final responseData = await _dioClient.post(
        '/upload/image',
        data: formData,
        onSendProgress: (sent, total) {
          final progress = ((sent / total) * 100).round();
          _logger.d('上传进度: $progress% ($sent/$total)');
          onProgress?.call(progress);
        },
      );

      _logger.d('API响应: 图片上传成功 - $responseData');
      return ImageUploadModel.fromJson(responseData['data']);
    } catch (e, stackTrace) {
      _logger.e('上传图片API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<List<ImageUploadModel>> uploadImages(
    List<String> imagePaths, {
    Function(int totalProgress)? onProgress,
  }) async {
    _logger.d('API调用: 批量上传图片 - ${imagePaths.length}张');
    
    try {
      final uploads = <ImageUploadModel>[];
      int completedCount = 0;

      for (final imagePath in imagePaths) {
        final upload = await uploadImage(
          imagePath,
          onProgress: (progress) {
            // 计算总体进度
            final totalProgress = ((completedCount * 100 + progress) / imagePaths.length).round();
            onProgress?.call(totalProgress);
          },
        );
        
        uploads.add(upload);
        completedCount++;
        
        // 更新总体进度
        final totalProgress = ((completedCount * 100) / imagePaths.length).round();
        onProgress?.call(totalProgress);
      }

      _logger.i('批量上传完成: ${uploads.length}张图片');
      return uploads;
    } catch (e, stackTrace) {
      _logger.e('批量上传图片失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<ImageUploadModel> getUploadStatus(String uploadId) async {
    _logger.d('API调用: 获取上传状态 - $uploadId');
    
    try {
      final responseData = await _dioClient.get('/upload/status/$uploadId');
      _logger.d('API响应: 上传状态 - $responseData');

      return ImageUploadModel.fromJson(responseData['data']);
    } catch (e, stackTrace) {
      _logger.e('获取上传状态API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> cancelUpload(String uploadId) async {
    _logger.d('API调用: 取消上传 - $uploadId');
    
    try {
      await _dioClient.post('/upload/cancel/$uploadId');
      _logger.d('API响应: 上传已取消');
    } catch (e, stackTrace) {
      _logger.e('取消上传API调用失败', e, stackTrace);
      rethrow;
    }
  }

  @override
  Future<void> deleteUploadedImage(String uploadId) async {
    _logger.d('API调用: 删除已上传图片 - $uploadId');
    
    try {
      await _dioClient.delete('/upload/$uploadId');
      _logger.d('API响应: 图片已删除');
    } catch (e, stackTrace) {
      _logger.e('删除图片API调用失败', e, stackTrace);
      rethrow;
    }
  }

  /// 根据文件名获取图片类型
  String _getImageType(String fileName) {
    final extension = fileName.toLowerCase().split('.').last;
    switch (extension) {
      case 'jpg':
      case 'jpeg':
        return 'jpeg';
      case 'png':
        return 'png';
      case 'heic':
        return 'heic';
      default:
        return 'jpeg';
    }
  }
}
