import 'package:dartz/dartz.dart';
import '../../../../core/error/failures.dart';
import '../../../../core/network/network_info.dart';
import '../../../../core/utils/log_service.dart';
import '../../domain/entities/generation_request.dart';
import '../../domain/entities/generation_result.dart';
import '../../domain/repositories/ai_generation_repository.dart';
import '../datasources/ai_generation_remote_datasource.dart';
import '../models/generation_request_model.dart';
import '../models/generation_result_model.dart';

/// AI生成仓库实现
class AiGenerationRepositoryImpl implements AiGenerationRepository {
  final AiGenerationRemoteDataSource _remoteDataSource;
  final NetworkInfo _networkInfo;
  final LogService _logger;

  AiGenerationRepositoryImpl(
    this._remoteDataSource,
    this._networkInfo,
    this._logger,
  );

  @override
  Future<Either<Failure, List<StyleTemplate>>> getStyleTemplates() async {
    _logger.i('仓库: 获取风格模板列表');
    
    if (await _networkInfo.isConnected()) {
      try {
        final templates = await _remoteDataSource.getStyleTemplates();
        _logger.i('仓库: 成功获取${templates.length}个风格模板');
        return Right(templates.map((model) => model.toEntity()).toList());
      } catch (e, stackTrace) {
        _logger.e('仓库: 获取风格模板失败', e, stackTrace);
        return Left(ServerFailure('获取风格模板失败: $e'));
      }
    } else {
      _logger.w('仓库: 网络连接不可用');
      return Left(NetworkFailure('网络连接不可用'));
    }
  }

  @override
  Future<Either<Failure, StyleTemplate>> getStyleTemplate(String templateId) async {
    _logger.i('仓库: 获取风格模板 - $templateId');
    
    if (await _networkInfo.isConnected()) {
      try {
        final template = await _remoteDataSource.getStyleTemplate(templateId);
        _logger.i('仓库: 成功获取风格模板 - ${template.name}');
        return Right(template.toEntity());
      } catch (e, stackTrace) {
        _logger.e('仓库: 获取风格模板失败', e, stackTrace);
        return Left(ServerFailure('获取风格模板失败: $e'));
      }
    } else {
      _logger.w('仓库: 网络连接不可用');
      return Left(NetworkFailure('网络连接不可用'));
    }
  }

  @override
  Future<Either<Failure, GenerationRequest>> submitGenerationRequest({
    required List<String> imagePaths,
    required String styleTemplateId,
    required Map<String, dynamic> customParameters,
  }) async {
    _logger.i('仓库: 提交生成请求');
    _logger.d('仓库: 参数 - 图片=${imagePaths.length}张, 风格=$styleTemplateId');
    
    if (await _networkInfo.isConnected()) {
      try {
        final request = await _remoteDataSource.submitGenerationRequest(
          imageUrls: imagePaths, // 这里假设imagePaths是已上传的URL
          styleTemplateId: styleTemplateId,
          customParameters: customParameters,
        );
        _logger.i('仓库: 成功提交生成请求 - ${request.id}');
        return Right(request.toEntity());
      } catch (e, stackTrace) {
        _logger.e('仓库: 提交生成请求失败', e, stackTrace);
        return Left(ServerFailure('提交生成请求失败: $e'));
      }
    } else {
      _logger.w('仓库: 网络连接不可用');
      return Left(NetworkFailure('网络连接不可用'));
    }
  }

  @override
  Future<Either<Failure, GenerationRequest>> getGenerationStatus(String requestId) async {
    _logger.d('仓库: 获取生成状态 - $requestId');
    
    if (await _networkInfo.isConnected()) {
      try {
        final request = await _remoteDataSource.getGenerationStatus(requestId);
        _logger.d('仓库: 生成状态 - ${request.status}');
        return Right(request.toEntity());
      } catch (e, stackTrace) {
        _logger.e('仓库: 获取生成状态失败', e, stackTrace);
        return Left(ServerFailure('获取生成状态失败: $e'));
      }
    } else {
      _logger.w('仓库: 网络连接不可用');
      return Left(NetworkFailure('网络连接不可用'));
    }
  }

  @override
  Future<Either<Failure, GenerationResult>> getGenerationResult(String requestId) async {
    _logger.i('仓库: 获取生成结果 - $requestId');
    
    if (await _networkInfo.isConnected()) {
      try {
        final result = await _remoteDataSource.getGenerationResult(requestId);
        _logger.i('仓库: 成功获取生成结果 - ${result.imageUrls.length}张图片');
        return Right(result.toEntity());
      } catch (e, stackTrace) {
        _logger.e('仓库: 获取生成结果失败', e, stackTrace);
        return Left(ServerFailure('获取生成结果失败: $e'));
      }
    } else {
      _logger.w('仓库: 网络连接不可用');
      return Left(NetworkFailure('网络连接不可用'));
    }
  }

  @override
  Future<Either<Failure, void>> cancelGenerationRequest(String requestId) async {
    _logger.i('仓库: 取消生成请求 - $requestId');
    
    if (await _networkInfo.isConnected()) {
      try {
        await _remoteDataSource.cancelGenerationRequest(requestId);
        _logger.i('仓库: 成功取消生成请求');
        return const Right(null);
      } catch (e, stackTrace) {
        _logger.e('仓库: 取消生成请求失败', e, stackTrace);
        return Left(ServerFailure('取消生成请求失败: $e'));
      }
    } else {
      _logger.w('仓库: 网络连接不可用');
      return Left(NetworkFailure('网络连接不可用'));
    }
  }

  @override
  Future<Either<Failure, List<GenerationResult>>> getGenerationHistory({
    int page = 1,
    int limit = 20,
  }) async {
    _logger.i('仓库: 获取生成历史 - page=$page, limit=$limit');
    
    if (await _networkInfo.isConnected()) {
      try {
        final results = await _remoteDataSource.getGenerationHistory(
          page: page,
          limit: limit,
        );
        _logger.i('仓库: 成功获取${results.length}条生成历史');
        return Right(results.map((model) => model.toEntity()).toList());
      } catch (e, stackTrace) {
        _logger.e('仓库: 获取生成历史失败', e, stackTrace);
        return Left(ServerFailure('获取生成历史失败: $e'));
      }
    } else {
      _logger.w('仓库: 网络连接不可用');
      return Left(NetworkFailure('网络连接不可用'));
    }
  }

  @override
  Future<Either<Failure, void>> deleteGenerationResult(String resultId) async {
    _logger.i('仓库: 删除生成结果 - $resultId');
    
    if (await _networkInfo.isConnected()) {
      try {
        await _remoteDataSource.deleteGenerationResult(resultId);
        _logger.i('仓库: 成功删除生成结果');
        return const Right(null);
      } catch (e, stackTrace) {
        _logger.e('仓库: 删除生成结果失败', e, stackTrace);
        return Left(ServerFailure('删除生成结果失败: $e'));
      }
    } else {
      _logger.w('仓库: 网络连接不可用');
      return Left(NetworkFailure('网络连接不可用'));
    }
  }

  @override
  Future<Either<Failure, String>> saveResultToLocal(String resultId, String imageUrl) async {
    _logger.i('仓库: 保存结果到本地 - $resultId');
    
    try {
      // TODO: 实现本地保存逻辑
      // 这里需要下载图片并保存到本地存储
      _logger.i('仓库: 成功保存到本地');
      return const Right('/local/path/to/saved/image.jpg');
    } catch (e, stackTrace) {
      _logger.e('仓库: 保存到本地失败', e, stackTrace);
      return Left(LocalStorageFailure('保存到本地失败: $e'));
    }
  }
}
