import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/generation_request.dart';

part 'generation_request_model.freezed.dart';
part 'generation_request_model.g.dart';

/// AI生成请求数据模型
@freezed
class GenerationRequestModel with _$GenerationRequestModel {
  const factory GenerationRequestModel({
    /// 请求ID
    required String id,
    /// 用户上传的图片路径列表
    required List<String> imagePaths,
    /// 选择的风格模板ID
    required String styleTemplateId,
    /// 自定义参数
    required Map<String, dynamic> customParameters,
    /// 请求创建时间
    required DateTime createdAt,
    /// 请求状态
    required GenerationStatus status,
    /// 错误信息（如果有）
    String? errorMessage,
  }) = _GenerationRequestModel;

  factory GenerationRequestModel.fromJson(Map<String, dynamic> json) =>
      _$GenerationRequestModelFromJson(json);

  /// 从领域实体转换
  factory GenerationRequestModel.fromEntity(GenerationRequest entity) {
    return GenerationRequestModel(
      id: entity.id,
      imagePaths: entity.imagePaths,
      styleTemplateId: entity.styleTemplateId,
      customParameters: entity.customParameters,
      createdAt: entity.createdAt,
      status: entity.status,
      errorMessage: entity.errorMessage,
    );
  }
}

/// 扩展方法：转换为领域实体
extension GenerationRequestModelX on GenerationRequestModel {
  GenerationRequest toEntity() {
    return GenerationRequest(
      id: id,
      imagePaths: imagePaths,
      styleTemplateId: styleTemplateId,
      customParameters: customParameters,
      createdAt: createdAt,
      status: status,
      errorMessage: errorMessage,
    );
  }
}
