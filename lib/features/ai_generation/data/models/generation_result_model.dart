import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/generation_result.dart';

part 'generation_result_model.freezed.dart';
part 'generation_result_model.g.dart';

/// AI生成结果数据模型
@freezed
class GenerationResultModel with _$GenerationResultModel {
  const factory GenerationResultModel({
    /// 结果ID
    required String id,
    /// 关联的请求ID
    required String requestId,
    /// 生成的图片URL列表
    required List<String> imageUrls,
    /// 生成的图片本地路径列表（用于缓存）
    List<String>? localImagePaths,
    /// 使用的风格模板信息
    required StyleTemplateModel styleTemplate,
    /// 生成参数
    required Map<String, dynamic> generationParameters,
    /// 生成完成时间
    required DateTime completedAt,
    /// 图片质量评分 (0-100)
    required int qualityScore,
    /// 人脸相似度评分 (0-100)
    required int similarityScore,
    /// 生成耗时（秒）
    required int processingTimeSeconds,
  }) = _GenerationResultModel;

  factory GenerationResultModel.fromJson(Map<String, dynamic> json) =>
      _$GenerationResultModelFromJson(json);

  /// 从领域实体转换
  factory GenerationResultModel.fromEntity(GenerationResult entity) {
    return GenerationResultModel(
      id: entity.id,
      requestId: entity.requestId,
      imageUrls: entity.imageUrls,
      localImagePaths: entity.localImagePaths,
      styleTemplate: StyleTemplateModel.fromEntity(entity.styleTemplate),
      generationParameters: entity.generationParameters,
      completedAt: entity.completedAt,
      qualityScore: entity.qualityScore,
      similarityScore: entity.similarityScore,
      processingTimeSeconds: entity.processingTimeSeconds,
    );
  }
}

/// 风格模板数据模型
@freezed
class StyleTemplateModel with _$StyleTemplateModel {
  const factory StyleTemplateModel({
    /// 模板ID
    required String id,
    /// 模板名称
    required String name,
    /// 模板分类
    required String category,
    /// 模板描述
    required String description,
    /// 预览图片URL
    required String previewImageUrl,
    /// AI提示词
    required String prompt,
    /// 负面提示词
    required String negativePrompt,
    /// 生成参数
    required StyleParametersModel parameters,
    /// 是否为高级模板
    @Default(false) bool isPremium,
  }) = _StyleTemplateModel;

  factory StyleTemplateModel.fromJson(Map<String, dynamic> json) =>
      _$StyleTemplateModelFromJson(json);

  /// 从领域实体转换
  factory StyleTemplateModel.fromEntity(StyleTemplate entity) {
    return StyleTemplateModel(
      id: entity.id,
      name: entity.name,
      category: entity.category,
      description: entity.description,
      previewImageUrl: entity.previewImageUrl,
      prompt: entity.prompt,
      negativePrompt: entity.negativePrompt,
      parameters: StyleParametersModel.fromEntity(entity.parameters),
      isPremium: entity.isPremium,
    );
  }
}

/// 风格参数数据模型
@freezed
class StyleParametersModel with _$StyleParametersModel {
  const factory StyleParametersModel({
    /// 生成步数
    @Default(30) int steps,
    /// 引导强度
    @Default(7.5) double guidanceScale,
    /// 强度
    @Default(0.8) double strength,
    /// 种子值（可选）
    int? seed,
    /// 输出分辨率
    @Default('1024x1024') String resolution,
  }) = _StyleParametersModel;

  factory StyleParametersModel.fromJson(Map<String, dynamic> json) =>
      _$StyleParametersModelFromJson(json);

  /// 从领域实体转换
  factory StyleParametersModel.fromEntity(StyleParameters entity) {
    return StyleParametersModel(
      steps: entity.steps,
      guidanceScale: entity.guidanceScale,
      strength: entity.strength,
      seed: entity.seed,
      resolution: entity.resolution,
    );
  }
}

/// 扩展方法：转换为领域实体
extension GenerationResultModelX on GenerationResultModel {
  GenerationResult toEntity() {
    return GenerationResult(
      id: id,
      requestId: requestId,
      imageUrls: imageUrls,
      localImagePaths: localImagePaths,
      styleTemplate: styleTemplate.toEntity(),
      generationParameters: generationParameters,
      completedAt: completedAt,
      qualityScore: qualityScore,
      similarityScore: similarityScore,
      processingTimeSeconds: processingTimeSeconds,
    );
  }
}

extension StyleTemplateModelX on StyleTemplateModel {
  StyleTemplate toEntity() {
    return StyleTemplate(
      id: id,
      name: name,
      category: category,
      description: description,
      previewImageUrl: previewImageUrl,
      prompt: prompt,
      negativePrompt: negativePrompt,
      parameters: parameters.toEntity(),
      isPremium: isPremium,
    );
  }
}

extension StyleParametersModelX on StyleParametersModel {
  StyleParameters toEntity() {
    return StyleParameters(
      steps: steps,
      guidanceScale: guidanceScale,
      strength: strength,
      seed: seed,
      resolution: resolution,
    );
  }
}
