import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/image_upload.dart';

part 'image_upload_model.freezed.dart';
part 'image_upload_model.g.dart';

/// 图片上传数据模型
@freezed
class ImageUploadModel with _$ImageUploadModel {
  const factory ImageUploadModel({
    /// 上传ID
    required String id,
    /// 本地文件路径
    required String localPath,
    /// 远程URL（上传成功后）
    String? remoteUrl,
    /// 文件名
    required String fileName,
    /// 文件大小（字节）
    required int fileSize,
    /// 文件类型
    required String mimeType,
    /// 上传状态
    required UploadStatus status,
    /// 上传进度 (0-100)
    @Default(0) int progress,
    /// 图片元数据
    ImageMetadataModel? metadata,
    /// 错误信息
    String? errorMessage,
    /// 创建时间
    required DateTime createdAt,
    /// 完成时间
    DateTime? completedAt,
  }) = _ImageUploadModel;

  factory ImageUploadModel.fromJson(Map<String, dynamic> json) =>
      _$ImageUploadModelFromJson(json);

  /// 从领域实体转换
  factory ImageUploadModel.fromEntity(ImageUpload entity) {
    return ImageUploadModel(
      id: entity.id,
      localPath: entity.localPath,
      remoteUrl: entity.remoteUrl,
      fileName: entity.fileName,
      fileSize: entity.fileSize,
      mimeType: entity.mimeType,
      status: entity.status,
      progress: entity.progress,
      metadata: entity.metadata != null 
          ? ImageMetadataModel.fromEntity(entity.metadata!)
          : null,
      errorMessage: entity.errorMessage,
      createdAt: entity.createdAt,
      completedAt: entity.completedAt,
    );
  }
}

/// 图片元数据数据模型
@freezed
class ImageMetadataModel with _$ImageMetadataModel {
  const factory ImageMetadataModel({
    /// 图片宽度
    required int width,
    /// 图片高度
    required int height,
    /// 是否检测到人脸
    required bool hasFace,
    /// 人脸数量
    @Default(0) int faceCount,
    /// 图片质量评分 (0-100)
    required int qualityScore,
    /// 是否模糊
    @Default(false) bool isBlurry,
    /// 光线充足度评分 (0-100)
    required int lightingScore,
    /// 人脸区域坐标 (x, y, width, height)
    List<FaceRegionModel>? faceRegions,
  }) = _ImageMetadataModel;

  factory ImageMetadataModel.fromJson(Map<String, dynamic> json) =>
      _$ImageMetadataModelFromJson(json);

  /// 从领域实体转换
  factory ImageMetadataModel.fromEntity(ImageMetadata entity) {
    return ImageMetadataModel(
      width: entity.width,
      height: entity.height,
      hasFace: entity.hasFace,
      faceCount: entity.faceCount,
      qualityScore: entity.qualityScore,
      isBlurry: entity.isBlurry,
      lightingScore: entity.lightingScore,
      faceRegions: entity.faceRegions
          ?.map((region) => FaceRegionModel.fromEntity(region))
          .toList(),
    );
  }
}

/// 人脸区域数据模型
@freezed
class FaceRegionModel with _$FaceRegionModel {
  const factory FaceRegionModel({
    /// X坐标
    required double x,
    /// Y坐标
    required double y,
    /// 宽度
    required double width,
    /// 高度
    required double height,
    /// 置信度 (0-1)
    required double confidence,
  }) = _FaceRegionModel;

  factory FaceRegionModel.fromJson(Map<String, dynamic> json) =>
      _$FaceRegionModelFromJson(json);

  /// 从领域实体转换
  factory FaceRegionModel.fromEntity(FaceRegion entity) {
    return FaceRegionModel(
      x: entity.x,
      y: entity.y,
      width: entity.width,
      height: entity.height,
      confidence: entity.confidence,
    );
  }
}

/// 扩展方法：转换为领域实体
extension ImageUploadModelX on ImageUploadModel {
  ImageUpload toEntity() {
    return ImageUpload(
      id: id,
      localPath: localPath,
      remoteUrl: remoteUrl,
      fileName: fileName,
      fileSize: fileSize,
      mimeType: mimeType,
      status: status,
      progress: progress,
      metadata: metadata?.toEntity(),
      errorMessage: errorMessage,
      createdAt: createdAt,
      completedAt: completedAt,
    );
  }
}

extension ImageMetadataModelX on ImageMetadataModel {
  ImageMetadata toEntity() {
    return ImageMetadata(
      width: width,
      height: height,
      hasFace: hasFace,
      faceCount: faceCount,
      qualityScore: qualityScore,
      isBlurry: isBlurry,
      lightingScore: lightingScore,
      faceRegions: faceRegions?.map((region) => region.toEntity()).toList(),
    );
  }
}

extension FaceRegionModelX on FaceRegionModel {
  FaceRegion toEntity() {
    return FaceRegion(
      x: x,
      y: y,
      width: width,
      height: height,
      confidence: confidence,
    );
  }
}
