import 'package:freezed_annotation/freezed_annotation.dart';
import '../../domain/entities/generation_request.dart';
import '../../domain/entities/generation_result.dart';
import '../../domain/entities/image_upload.dart';

part 'ai_generation_state.freezed.dart';

/// AI生成状态
@freezed
class AiGenerationState with _$AiGenerationState {
  /// 初始状态
  const factory AiGenerationState.initial() = _Initial;
  
  /// 加载中
  const factory AiGenerationState.loading() = _Loading;
  
  /// 风格模板已加载
  const factory AiGenerationState.styleTemplatesLoaded(
    List<StyleTemplate> templates,
  ) = _StyleTemplatesLoaded;
  
  /// 上传中
  const factory AiGenerationState.uploading() = _Uploading;
  
  /// 上传进度
  const factory AiGenerationState.uploadProgress(int progress) = _UploadProgress;
  
  /// 图片已上传
  const factory AiGenerationState.imagesUploaded(
    List<ImageUpload> uploads,
  ) = _ImagesUploaded;
  
  /// 生成中
  const factory AiGenerationState.generating() = _Generating;
  
  /// 生成进度
  const factory AiGenerationState.generationProgress(
    GenerationRequest request,
  ) = _GenerationProgress;
  
  /// 生成完成
  const factory AiGenerationState.generationCompleted(
    GenerationResult result,
  ) = _GenerationCompleted;
  
  /// 错误状态
  const factory AiGenerationState.error(String message) = _Error;
}
