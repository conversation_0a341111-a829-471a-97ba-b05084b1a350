import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:image_picker/image_picker.dart';
import '../../../../core/services/clean_permission_manager.dart';
import '../../../../core/utils/log_service.dart';
import '../../../../shared/widgets/custom_button.dart';
import '../../../../shared/widgets/loading_overlay.dart';
import '../widgets/image_upload_card.dart';

/// 图片上传页面
@RoutePage()
class ImageUploadScreen extends ConsumerStatefulWidget {
  const ImageUploadScreen({super.key});

  @override
  ConsumerState<ImageUploadScreen> createState() => _ImageUploadScreenState();
}

class _ImageUploadScreenState extends ConsumerState<ImageUploadScreen> {
  final LogService _logger = LogService();
  final ImagePicker _picker = ImagePicker();
  final CleanPermissionManager _permissionManager = CleanPermissionManager();
  List<String> _selectedImages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _logger.i('图片上传页面初始化');
    _checkInitialPermissions();
  }

  /// 检查初始权限状态
  Future<void> _checkInitialPermissions() async {
    _logger.d('检查初始权限状态');
    // 简化初始权限检查，仅在需要时再申请
  }

  /// 请求通知权限（简化处理）
  Future<void> _requestNotificationPermission() async {
    _logger.i('通知权限申请暂时跳过');
    _showInfoSnackBar('您可以稍后在设置中开启通知权限');
  }

  @override
  Widget build(BuildContext context) {
    // final aiGenerationState = ref.watch(aiGenerationProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('上传照片'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
      ),
      body: LoadingOverlay(
        isLoading: _isLoading,
        child: Padding(
          padding: EdgeInsets.all(16.w),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInstructions(),
              SizedBox(height: 24.h),
              _buildImageGrid(),
              SizedBox(height: 24.h),
              _buildUploadButtons(),
              const Spacer(),
              _buildContinueButton(),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建说明文本
  Widget _buildInstructions() {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.info_outline, color: Colors.blue.shade600, size: 20.sp),
              SizedBox(width: 8.w),
              Text(
                '上传要求',
                style: TextStyle(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.bold,
                  color: Colors.blue.shade600,
                ),
              ),
            ],
          ),
          SizedBox(height: 8.h),
          Text(
            '• 支持JPG、PNG、HEIC格式\n'
            '• 单张图片不超过10MB\n'
            '• 建议上传1-3张清晰的正面照\n'
            '• 确保人脸清晰可见，光线充足',
            style: TextStyle(
              fontSize: 14.sp,
              color: Colors.grey.shade700,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建图片网格
  Widget _buildImageGrid() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '已选择照片 (${_selectedImages.length}/3)',
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.bold,
          ),
        ),
        SizedBox(height: 12.h),
        SizedBox(
          height: 120.h,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _selectedImages.length + (_selectedImages.length < 3 ? 1 : 0),
            itemBuilder: (context, index) {
              if (index < _selectedImages.length) {
                return ImageUploadCard(
                  imagePath: _selectedImages[index],
                  onRemove: () => _removeImage(index),
                );
              } else {
                return _buildAddImageCard();
              }
            },
          ),
        ),
      ],
    );
  }

  /// 构建添加图片卡片
  Widget _buildAddImageCard() {
    return Container(
      width: 120.w,
      margin: EdgeInsets.only(right: 12.w),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300, width: 2),
        borderRadius: BorderRadius.circular(12.r),
        color: Colors.grey.shade50,
      ),
      child: InkWell(
        onTap: _showImageSourceDialog,
        borderRadius: BorderRadius.circular(12.r),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.add_photo_alternate_outlined,
              size: 32.sp,
              color: Colors.grey.shade600,
            ),
            SizedBox(height: 8.h),
            Text(
              '添加照片',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建上传按钮
  Widget _buildUploadButtons() {
    return Row(
      children: [
        Expanded(
          child: CustomButton(
            text: '从相册选择',
            onPressed: () => _pickImages(ImageSource.gallery),
            backgroundColor: Colors.blue.shade50,
            textColor: Colors.blue.shade700,
            icon: Icons.photo_library_outlined,
          ),
        ),
        SizedBox(width: 12.w),
        Expanded(
          child: CustomButton(
            text: '拍照',
            onPressed: () => _pickImages(ImageSource.camera),
            backgroundColor: Colors.green.shade50,
            textColor: Colors.green.shade700,
            icon: Icons.camera_alt_outlined,
          ),
        ),
      ],
    );
  }

  /// 构建继续按钮
  Widget _buildContinueButton() {
    return CustomButton(
      text: '继续选择风格',
      onPressed: _selectedImages.isNotEmpty ? _continueToStyleSelection : null,
      isFullWidth: true,
    );
  }

  /// 显示图片来源选择对话框
  void _showImageSourceDialog() {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20.r)),
      ),
      builder: (context) => Container(
        padding: EdgeInsets.all(20.w),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              leading: const Icon(Icons.photo_library_outlined),
              title: const Text('从相册选择'),
              onTap: () {
                Navigator.pop(context);
                _pickImages(ImageSource.gallery);
              },
            ),
            ListTile(
              leading: const Icon(Icons.camera_alt_outlined),
              title: const Text('拍照'),
              onTap: () {
                Navigator.pop(context);
                _pickImages(ImageSource.camera);
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 选择图片
  Future<void> _pickImages(ImageSource source) async {
    _logger.d('开始选择图片: ${source.name}');

    try {
      // 检查权限
      if (!await _checkPermissions(source)) {
        _logger.w('权限检查失败');
        return;
      }

      setState(() => _isLoading = true);

      if (source == ImageSource.gallery) {
        _logger.d('从相册选择多张图片');
        final images = await _picker.pickMultiImage(
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );

        if (images.isNotEmpty) {
          _logger.i('从相册选择了${images.length}张图片');
          _addImages(images.map((image) => image.path).toList());
        } else {
          _logger.d('用户取消了图片选择');
        }
      } else {
        _logger.d('使用相机拍照');
        final image = await _picker.pickImage(
          source: source,
          maxWidth: 1920,
          maxHeight: 1920,
          imageQuality: 85,
        );

        if (image != null) {
          _logger.i('拍摄了一张照片: ${image.path}');
          _addImages([image.path]);
        } else {
          _logger.d('用户取消了拍照');
        }
      }
    } catch (e, stackTrace) {
      _logger.e('选择图片失败', e, stackTrace);
      String errorMessage = '选择图片失败';
      if (e.toString().contains('camera_access_denied')) {
        errorMessage = '相机权限被拒绝，请在设置中开启';
      } else if (e.toString().contains('photo_access_denied')) {
        errorMessage = '相册权限被拒绝，请在设置中开启';
      }
      _showErrorSnackBar(errorMessage);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 检查权限
  Future<bool> _checkPermissions(ImageSource source) async {
    try {
      if (source == ImageSource.camera) {
        return await _checkCameraPermission();
      } else {
        return await _checkPhotosPermission();
      }
    } catch (e) {
      _logger.e('权限检查失败: $e');
      _showErrorSnackBar('权限检查失败，请在设置中手动开启权限');
      return false;
    }
  }

  /// 检查相机权限（使用清洁权限管理器）
  Future<bool> _checkCameraPermission() async {
    final hasPermission = await _permissionManager.requestCameraPermission(context);

    if (hasPermission) {
      _logger.i('相机权限获得授权');
    } else {
      _logger.w('相机权限申请失败');
    }

    return hasPermission;
  }

  /// 检查相册权限（使用清洁权限管理器）
  Future<bool> _checkPhotosPermission() async {
    final hasPermission = await _permissionManager.requestPhotosPermission(context);

    if (hasPermission) {
      _logger.i('相册权限获得授权');
    } else {
      _logger.w('相册权限申请失败');
    }

    return hasPermission;
  }



  /// 添加图片
  void _addImages(List<String> imagePaths) {
    final remainingSlots = 3 - _selectedImages.length;
    final imagesToAdd = imagePaths.take(remainingSlots).toList();
    
    setState(() {
      _selectedImages.addAll(imagesToAdd);
    });
    
    _logger.i('添加了${imagesToAdd.length}张图片');
    
    if (imagePaths.length > remainingSlots) {
      _showErrorSnackBar('最多只能选择3张图片');
    }
  }

  /// 移除图片
  void _removeImage(int index) {
    setState(() {
      _selectedImages.removeAt(index);
    });
    _logger.d('移除了第${index + 1}张图片');
  }

  /// 继续到风格选择
  void _continueToStyleSelection() {
    _logger.i('继续到风格选择页面');
    // TODO: 导航到风格选择页面
    // context.router.push(StyleSelectionRoute(imagePaths: _selectedImages));
  }

  /// 显示错误提示
  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 4),
      ),
    );
  }

  /// 显示成功提示
  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// 显示信息提示
  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.info_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.blue,
        duration: const Duration(seconds: 3),
      ),
    );
  }
}
