import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/di/injection.dart';
// import '../../domain/entities/generation_request.dart';
import '../../domain/entities/generation_result.dart';
import '../../domain/entities/image_upload.dart';
import '../../domain/usecases/generate_wedding_photo_usecase.dart';
import '../../domain/usecases/get_style_templates_usecase.dart';
import '../../domain/usecases/upload_images_usecase.dart';
import '../state/ai_generation_state.dart';

/// AI生成状态提供者
final aiGenerationProvider = StateNotifierProvider<AiGenerationNotifier, AiGenerationState>(
  (ref) => AiGenerationNotifier(
    getIt<GetStyleTemplatesUseCase>(),
    getIt<UploadImagesUseCase>(),
    getIt<GenerateWeddingPhotoUseCase>(),
  ),
);

/// 风格模板列表提供者
final styleTemplatesProvider = FutureProvider<List<StyleTemplate>>((ref) async {
  final useCase = getIt<GetStyleTemplatesUseCase>();
  final result = await useCase();
  
  return result.fold(
    (failure) => throw Exception(failure.message),
    (templates) => templates,
  );
});

/// 选中的风格模板提供者
final selectedStyleTemplateProvider = StateProvider<StyleTemplate?>((ref) => null);

/// 上传的图片列表提供者
final uploadedImagesProvider = StateProvider<List<ImageUpload>>((ref) => []);

/// 生成参数提供者
final generationParametersProvider = StateProvider<Map<String, dynamic>>((ref) => {
  'steps': 30,
  'guidance_scale': 7.5,
  'strength': 0.8,
  'resolution': '1024x1024',
});

/// AI生成状态管理器
class AiGenerationNotifier extends StateNotifier<AiGenerationState> {
  final GetStyleTemplatesUseCase _getStyleTemplatesUseCase;
  final UploadImagesUseCase _uploadImagesUseCase;
  final GenerateWeddingPhotoUseCase _generateWeddingPhotoUseCase;

  AiGenerationNotifier(
    this._getStyleTemplatesUseCase,
    this._uploadImagesUseCase,
    this._generateWeddingPhotoUseCase,
  ) : super(const AiGenerationState.initial());

  /// 获取风格模板列表
  Future<void> loadStyleTemplates() async {
    state = const AiGenerationState.loading();
    
    final result = await _getStyleTemplatesUseCase();
    
    result.fold(
      (failure) => state = AiGenerationState.error(failure.message),
      (templates) => state = AiGenerationState.styleTemplatesLoaded(templates),
    );
  }

  /// 上传图片
  Future<void> uploadImages(List<String> imagePaths) async {
    state = const AiGenerationState.uploading();
    
    final result = await _uploadImagesUseCase(
      UploadImagesParams(
        imagePaths: imagePaths,
        onProgress: (progress) {
          state = AiGenerationState.uploadProgress(progress);
        },
      ),
    );
    
    result.fold(
      (failure) => state = AiGenerationState.error(failure.message),
      (uploads) => state = AiGenerationState.imagesUploaded(uploads),
    );
  }

  /// 生成婚纱照
  Future<void> generateWeddingPhoto({
    required List<String> imagePaths,
    required String styleTemplateId,
    required Map<String, dynamic> customParameters,
  }) async {
    state = const AiGenerationState.generating();
    
    final result = await _generateWeddingPhotoUseCase(
      GenerateWeddingPhotoParams(
        imagePaths: imagePaths,
        styleTemplateId: styleTemplateId,
        customParameters: customParameters,
        onProgress: (request) {
          state = AiGenerationState.generationProgress(request);
        },
      ),
    );
    
    result.fold(
      (failure) => state = AiGenerationState.error(failure.message),
      (result) => state = AiGenerationState.generationCompleted(result),
    );
  }

  /// 重置状态
  void reset() {
    state = const AiGenerationState.initial();
  }

  /// 清除错误
  void clearError() {
    state = const AiGenerationState.initial();
  }
}
