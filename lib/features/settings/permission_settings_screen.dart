import 'package:flutter/material.dart';
import '../../core/services/clean_permission_manager.dart';

/// 权限设置页面
/// 让用户可以查看和管理应用权限
class PermissionSettingsScreen extends StatefulWidget {
  const PermissionSettingsScreen({super.key});

  @override
  State<PermissionSettingsScreen> createState() => _PermissionSettingsScreenState();
}

class _PermissionSettingsScreenState extends State<PermissionSettingsScreen> {
  final CleanPermissionManager _permissionManager = CleanPermissionManager();
  Map<String, bool> _permissionStatus = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  /// 检查所有权限状态
  Future<void> _checkPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      final status = await _permissionManager.checkPermissionStatus();
      setState(() => _permissionStatus = status);
    } catch (e) {
      print('检查权限状态失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('权限设置'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkPermissions,
            tooltip: '刷新权限状态',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPermissionOverview(),
                  const SizedBox(height: 24),
                  _buildPermissionList(),
                  const SizedBox(height: 24),
                  _buildPermissionTips(),
                ],
              ),
            ),
    );
  }

  /// 构建权限概览卡片
  Widget _buildPermissionOverview() {
    final photosGranted = _permissionStatus['photos'] ?? false;
    final cameraGranted = _permissionStatus['camera'] ?? false;
    final allGranted = photosGranted && cameraGranted;

    return Card(
      elevation: 4,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  allGranted ? Icons.verified_user : Icons.warning,
                  color: allGranted ? Colors.green : Colors.orange,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        allGranted ? '权限配置完成' : '需要配置权限',
                        style: const TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        allGranted 
                            ? '所有必要权限已开启，可以正常使用所有功能'
                            : '部分权限未开启，可能影响功能使用',
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (!allGranted) ...[
              const SizedBox(height: 16),
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _requestAllPermissions,
                  icon: const Icon(Icons.security),
                  label: const Text('一键开启所有权限'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.blue,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建权限列表
  Widget _buildPermissionList() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '权限管理',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // 相册权限
            _buildPermissionItem(
              title: '相册权限',
              subtitle: '选择照片生成AI婚纱照',
              icon: Icons.photo_library,
              color: Colors.blue,
              isGranted: _permissionStatus['photos'] ?? false,
              onTap: () => _requestPhotosPermission(),
            ),
            
            const Divider(height: 32),
            
            // 相机权限
            _buildPermissionItem(
              title: '相机权限',
              subtitle: '拍摄照片生成AI婚纱照',
              icon: Icons.camera_alt,
              color: Colors.green,
              isGranted: _permissionStatus['camera'] ?? false,
              onTap: () => _requestCameraPermission(),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建单个权限项
  Widget _buildPermissionItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required bool isGranted,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Row(
          children: [
            // 图标
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(width: 16),
            
            // 文本信息
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            
            // 状态指示器
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: isGranted ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    isGranted ? Icons.check_circle : Icons.cancel,
                    color: isGranted ? Colors.green : Colors.red,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    isGranted ? '已开启' : '未开启',
                    style: TextStyle(
                      fontSize: 12,
                      color: isGranted ? Colors.green : Colors.red,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建权限提示
  Widget _buildPermissionTips() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  '权限说明',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '为什么需要这些权限？',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('• 相册权限：选择您的照片进行AI处理'),
                  const Text('• 相机权限：拍摄新照片进行AI处理'),
                  const SizedBox(height: 12),
                  const Text(
                    '隐私保护承诺：',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('• 只在您主动使用功能时访问'),
                  const Text('• 不会在后台偷偷访问您的内容'),
                  const Text('• 所有处理都在本地进行'),
                  const Text('• 您可以随时撤销权限'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 请求所有权限
  Future<void> _requestAllPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      await _permissionManager.requestImagePermissions(context);
      await _checkPermissions();
    } catch (e) {
      print('请求权限失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 请求相册权限
  Future<void> _requestPhotosPermission() async {
    setState(() => _isLoading = true);
    
    try {
      await _permissionManager.requestPhotosPermission(context);
      await _checkPermissions();
    } catch (e) {
      print('请求相册权限失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  /// 请求相机权限
  Future<void> _requestCameraPermission() async {
    setState(() => _isLoading = true);
    
    try {
      await _permissionManager.requestCameraPermission(context);
      await _checkPermissions();
    } catch (e) {
      print('请求相机权限失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
