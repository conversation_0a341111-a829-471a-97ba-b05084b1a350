import 'package:auto_route/auto_route.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import '../../../../core/router/app_router.dart';
import '../../../../core/theme/app_theme.dart';
import '../../../../shared/widgets/custom_button.dart';

@RoutePage()
class HomeScreen extends ConsumerWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('首页'),
        actions: [
          IconButton(
            icon: const Icon(Icons.brightness_6),
            onPressed: () => AppTheme.toggleThemeMode(ref),
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.all(16.w),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 欢迎区域
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(24.w),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [Colors.pink.shade100, Colors.purple.shade100],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(16.r),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '赫拉 AI婚纱照',
                    style: TextStyle(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.bold,
                      color: Colors.purple.shade800,
                    ),
                  ),
                  SizedBox(height: 8.h),
                  Text(
                    '用AI技术生成专属婚纱照\n让每一对恋人都能拥有梦幻婚纱照',
                    style: TextStyle(
                      fontSize: 16.sp,
                      color: Colors.purple.shade600,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
            SizedBox(height: 32.h),

            // 功能区域
            Text(
              '开始创作',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),

            // AI生成按钮
            CustomButton(
              text: '开始生成婚纱照',
              onPressed: () {
                context.router.push(const ImageUploadRoute());
              },
              icon: Icons.auto_awesome,
              isFullWidth: true,
              backgroundColor: Colors.pink.shade400,
            ),
            SizedBox(height: 16.h),

            // 功能介绍卡片
            _buildFeatureCard(
              icon: Icons.upload_file,
              title: '上传照片',
              description: '支持JPG、PNG格式，最多3张照片',
              color: Colors.blue,
            ),
            SizedBox(height: 12.h),
            _buildFeatureCard(
              icon: Icons.palette,
              title: '选择风格',
              description: '多种婚纱风格模板，总有一款适合你',
              color: Colors.green,
            ),
            SizedBox(height: 12.h),
            _buildFeatureCard(
              icon: Icons.auto_awesome,
              title: 'AI生成',
              description: '先进AI技术，生成高质量婚纱照',
              color: Colors.orange,
            ),

            SizedBox(height: 32.h),

            // Demo案例展示
            Text(
              '精选案例',
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.bold,
              ),
            ),
            SizedBox(height: 16.h),

            _buildDemoSection(context),
          ],
        ),
      ),
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Container(
            width: 48.w,
            height: 48.w,
            decoration: BoxDecoration(
              color: color.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12.r),
            ),
            child: Icon(
              icon,
              color: color,
              size: 24.sp,
            ),
          ),
          SizedBox(width: 16.w),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
                SizedBox(height: 4.h),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建Demo展示区域
  Widget _buildDemoSection(BuildContext context) {
    return Column(
      children: [
        // Demo案例网格
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 2,
            crossAxisSpacing: 12.w,
            mainAxisSpacing: 12.h,
            childAspectRatio: 0.75,
          ),
          itemCount: _demoItems.length,
          itemBuilder: (context, index) {
            final demo = _demoItems[index];
            return _buildDemoCard(context, demo);
          },
        ),

        SizedBox(height: 16.h),

        // 查看更多按钮
        CustomButton(
          text: '查看更多案例',
          onPressed: () {
            // TODO: 导航到案例展示页面
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(content: Text('更多案例功能开发中...')),
            );
          },
          backgroundColor: Colors.grey.shade100,
          textColor: Colors.grey.shade700,
          icon: Icons.arrow_forward,
        ),
      ],
    );
  }

  /// 构建Demo卡片
  Widget _buildDemoCard(BuildContext context, DemoItem demo) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16.r),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16.r),
        child: Stack(
          children: [
            // 背景图片
            Container(
              width: double.infinity,
              height: double.infinity,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: demo.gradientColors,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
              ),
              child: Icon(
                demo.icon,
                size: 48.sp,
                color: Colors.white.withOpacity(0.3),
              ),
            ),

            // 内容
            Positioned(
              bottom: 0,
              left: 0,
              right: 0,
              child: Container(
                padding: EdgeInsets.all(12.w),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.transparent,
                      Colors.black.withOpacity(0.7),
                    ],
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      demo.title,
                      style: TextStyle(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    Text(
                      demo.description,
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white.withOpacity(0.9),
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
            ),

            // 点击效果
            Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  _showDemoDetail(context, demo);
                },
                child: Container(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示Demo详情
  void _showDemoDetail(BuildContext context, DemoItem demo) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(demo.title),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              height: 200.h,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: demo.gradientColors,
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12.r),
              ),
              child: Icon(
                demo.icon,
                size: 64.sp,
                color: Colors.white.withOpacity(0.5),
              ),
            ),
            SizedBox(height: 16.h),
            Text(
              demo.description,
              style: TextStyle(fontSize: 14.sp),
            ),
            SizedBox(height: 8.h),
            Text(
              '特点：${demo.features.join('、')}',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('关闭'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.router.push(const ImageUploadRoute());
            },
            child: const Text('立即体验'),
          ),
        ],
      ),
    );
  }

  /// Demo数据
  static final List<DemoItem> _demoItems = [
    DemoItem(
      title: '经典白纱',
      description: '纯洁优雅的经典白色婚纱，适合教堂婚礼',
      icon: Icons.favorite,
      gradientColors: [Colors.pink.shade200, Colors.purple.shade200],
      features: ['纯白色调', '蕾丝花边', '拖尾设计'],
    ),
    DemoItem(
      title: '复古宫廷',
      description: '华丽复古的宫廷风格，展现贵族气质',
      icon: Icons.castle,
      gradientColors: [Colors.amber.shade200, Colors.orange.shade200],
      features: ['金色装饰', '宫廷风格', '华丽细节'],
    ),
    DemoItem(
      title: '森系自然',
      description: '清新自然的森系风格，户外婚礼首选',
      icon: Icons.nature,
      gradientColors: [Colors.green.shade200, Colors.teal.shade200],
      features: ['自然色调', '花卉装饰', '轻盈材质'],
    ),
    DemoItem(
      title: '现代简约',
      description: '简洁大方的现代设计，突出新娘气质',
      icon: Icons.auto_awesome,
      gradientColors: [Colors.blue.shade200, Colors.indigo.shade200],
      features: ['简约线条', '现代剪裁', '优雅大方'],
    ),
  ];
}

/// Demo项目数据类
class DemoItem {
  final String title;
  final String description;
  final IconData icon;
  final List<Color> gradientColors;
  final List<String> features;

  DemoItem({
    required this.title,
    required this.description,
    required this.icon,
    required this.gradientColors,
    required this.features,
  });
}