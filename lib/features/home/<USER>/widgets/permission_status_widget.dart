import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import '../../../../core/services/clean_permission_manager.dart';

/// 首页权限状态显示组件
/// 显示当前权限状态，并提供快速权限设置入口
class PermissionStatusWidget extends StatefulWidget {
  const PermissionStatusWidget({super.key});

  @override
  State<PermissionStatusWidget> createState() => _PermissionStatusWidgetState();
}

class _PermissionStatusWidgetState extends State<PermissionStatusWidget> {
  final CleanPermissionManager _permissionManager = CleanPermissionManager();
  Map<String, bool> _permissionStatus = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  /// 检查权限状态
  Future<void> _checkPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      final status = await _permissionManager.checkPermissionStatus();
      setState(() => _permissionStatus = status);
    } catch (e) {
      print('检查权限状态失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    final photosGranted = _permissionStatus['photos'] ?? false;
    final cameraGranted = _permissionStatus['camera'] ?? false;
    final allGranted = photosGranted && cameraGranted;

    if (_isLoading) {
      return Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          color: Colors.grey.withOpacity(0.1),
          borderRadius: BorderRadius.circular(12.r),
        ),
        child: Row(
          children: [
            SizedBox(
              width: 16.w,
              height: 16.w,
              child: const CircularProgressIndicator(strokeWidth: 2),
            ),
            SizedBox(width: 12.w),
            const Text('检查权限状态...'),
          ],
        ),
      );
    }

    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: allGranted 
            ? Colors.green.withOpacity(0.1)
            : Colors.orange.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(
          color: allGranted 
              ? Colors.green.withOpacity(0.3)
              : Colors.orange.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                allGranted ? Icons.verified_user : Icons.warning,
                color: allGranted ? Colors.green : Colors.orange,
                size: 20.w,
              ),
              SizedBox(width: 8.w),
              Expanded(
                child: Text(
                  allGranted ? '权限已配置' : '需要配置权限',
                  style: TextStyle(
                    fontSize: 16.sp,
                    fontWeight: FontWeight.bold,
                    color: allGranted ? Colors.green : Colors.orange,
                  ),
                ),
              ),
              if (!allGranted)
                TextButton(
                  onPressed: _requestAllPermissions,
                  child: Text(
                    '立即配置',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.orange,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: 8.h),
          Row(
            children: [
              _buildPermissionChip('相册', photosGranted, Icons.photo_library),
              SizedBox(width: 8.w),
              _buildPermissionChip('相机', cameraGranted, Icons.camera_alt),
            ],
          ),
          if (!allGranted) ...[
            SizedBox(height: 8.h),
            Text(
              '配置权限后可以正常选择照片和拍照',
              style: TextStyle(
                fontSize: 12.sp,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建权限状态芯片
  Widget _buildPermissionChip(String name, bool isGranted, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 4.h),
      decoration: BoxDecoration(
        color: isGranted 
            ? Colors.green.withOpacity(0.2)
            : Colors.red.withOpacity(0.2),
        borderRadius: BorderRadius.circular(12.r),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 14.w,
            color: isGranted ? Colors.green : Colors.red,
          ),
          SizedBox(width: 4.w),
          Text(
            name,
            style: TextStyle(
              fontSize: 12.sp,
              color: isGranted ? Colors.green : Colors.red,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// 请求所有权限
  Future<void> _requestAllPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      await _permissionManager.requestImagePermissions(context);
      await _checkPermissions();
    } catch (e) {
      print('请求权限失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
