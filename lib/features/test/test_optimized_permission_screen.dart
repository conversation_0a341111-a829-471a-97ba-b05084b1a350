import 'package:flutter/material.dart';
import '../../core/services/optimized_permission_service.dart';
import '../../shared/widgets/simple_image_picker.dart';

/// 测试优化权限服务的页面
class TestOptimizedPermissionScreen extends StatefulWidget {
  const TestOptimizedPermissionScreen({super.key});

  @override
  State<TestOptimizedPermissionScreen> createState() => _TestOptimizedPermissionScreenState();
}

class _TestOptimizedPermissionScreenState extends State<TestOptimizedPermissionScreen> {
  final OptimizedPermissionService _permissionService = OptimizedPermissionService();
  Map<String, bool> _permissionStatuses = {};
  List<String> _selectedImages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkAllPermissions();
  }

  /// 检查所有权限状态
  Future<void> _checkAllPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      print('🔍 检查权限状态...');
      final results = await _permissionService.checkAllPermissions();
      
      setState(() {
        _permissionStatuses = results;
      });
      
      print('📊 权限状态: 相册=${results['photos']}, 相机=${results['camera']}');
    } catch (e) {
      print('检查权限状态失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('测试优化权限服务'),
        backgroundColor: Colors.white,
        foregroundColor: Colors.black,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkAllPermissions,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildPermissionStatusCard(),
                  const SizedBox(height: 20),
                  _buildPermissionTestButtons(),
                  const SizedBox(height: 20),
                  _buildImagePickerTest(),
                  const SizedBox(height: 20),
                  _buildSelectedImagesDisplay(),
                ],
              ),
            ),
    );
  }

  /// 构建权限状态卡片
  Widget _buildPermissionStatusCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '当前权限状态',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            _buildPermissionStatusRow('相册权限', _permissionStatuses['photos'] ?? false),
            const SizedBox(height: 8),
            _buildPermissionStatusRow('相机权限', _permissionStatuses['camera'] ?? false),
          ],
        ),
      ),
    );
  }

  /// 构建权限状态行
  Widget _buildPermissionStatusRow(String name, bool hasPermission) {
    return Row(
      children: [
        Icon(
          hasPermission ? Icons.check_circle : Icons.cancel,
          color: hasPermission ? Colors.green : Colors.red,
          size: 20,
        ),
        const SizedBox(width: 8),
        Text(name),
        const Spacer(),
        Text(
          hasPermission ? '已授权' : '未授权',
          style: TextStyle(
            color: hasPermission ? Colors.green : Colors.red,
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  /// 构建权限测试按钮
  Widget _buildPermissionTestButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '权限测试',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testPhotosPermission,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('测试相册权限'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testCameraPermission,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('测试相机权限'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建图片选择器测试
  Widget _buildImagePickerTest() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '图片选择器测试',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            SimpleImagePicker(
              onImagesSelected: (images) {
                setState(() {
                  _selectedImages = images;
                });
                print('选择的图片: $images');
                _showSnackBar('成功选择${images.length}张图片', Colors.green);
              },
              maxImages: 3,
              allowMultiple: true,
              buttonText: '选择照片 (最多3张)',
            ),
          ],
        ),
      ),
    );
  }

  /// 构建已选择图片显示
  Widget _buildSelectedImagesDisplay() {
    if (_selectedImages.isEmpty) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '已选择的图片 (${_selectedImages.length})',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 100,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Image.network(
                        _selectedImages[index],
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            color: Colors.grey.shade200,
                            child: const Icon(Icons.image, color: Colors.grey),
                          );
                        },
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 测试相册权限
  Future<void> _testPhotosPermission() async {
    print('🏞️ 开始测试相册权限...');
    
    final hasPermission = await _permissionService.requestPhotosPermission(context);
    
    if (hasPermission) {
      print('✅ 相册权限获取成功!');
      _showSnackBar('相册权限获取成功!', Colors.green);
    } else {
      print('❌ 相册权限获取失败!');
      _showSnackBar('相册权限获取失败!', Colors.red);
    }
    
    // 刷新权限状态
    await _checkAllPermissions();
  }

  /// 测试相机权限
  Future<void> _testCameraPermission() async {
    print('📷 开始测试相机权限...');
    
    final hasPermission = await _permissionService.requestCameraPermission(context);
    
    if (hasPermission) {
      print('✅ 相机权限获取成功!');
      _showSnackBar('相机权限获取成功!', Colors.green);
    } else {
      print('❌ 相机权限获取失败!');
      _showSnackBar('相机权限获取失败!', Colors.red);
    }
    
    // 刷新权限状态
    await _checkAllPermissions();
  }

  /// 显示提示消息
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
      ),
    );
  }
}
