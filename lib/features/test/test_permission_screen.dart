import 'dart:io';
import 'package:flutter/material.dart';
import '../../shared/widgets/simple_image_picker.dart';
import '../../core/services/simple_permission_service.dart';

/// 权限测试页面
class TestPermissionScreen extends StatefulWidget {
  const TestPermissionScreen({super.key});

  @override
  State<TestPermissionScreen> createState() => _TestPermissionScreenState();
}

class _TestPermissionScreenState extends State<TestPermissionScreen> {
  final SimplePermissionService _permissionService = SimplePermissionService();
  List<String> _selectedImages = [];
  bool _photosPermission = false;
  bool _cameraPermission = false;
  List<String> _debugMessages = [];

  @override
  void initState() {
    super.initState();
    _checkPermissionStatus();
    
    // 5秒后再次检查权限状态，以防有延迟
    Future.delayed(const Duration(seconds: 2), () {
      if (mounted) {
        _checkPermissionStatus();
      }
    });
  }

  /// 添加调试信息
  void _addDebugMessage(String message) {
    setState(() {
      _debugMessages.insert(0, '${DateTime.now().toString().substring(11, 19)} $message');
      if (_debugMessages.length > 10) {
        _debugMessages.removeLast();
      }
    });
    print(message);
  }

  /// 检查权限状态
  Future<void> _checkPermissionStatus() async {
    _addDebugMessage('🔍 检查权限状态...');
    
    final photos = await _permissionService.hasPhotosPermission();
    final camera = await _permissionService.hasCameraPermission();
    
    setState(() {
      _photosPermission = photos;
      _cameraPermission = camera;
    });
    
    _addDebugMessage('📊 权限状态: 相册=$photos, 相机=$camera');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('权限测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
      ),
            body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
              // 操作指引
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16.0),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Colors.orange.shade100, Colors.orange.shade50],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.orange.shade700, size: 24),
                        const SizedBox(width: 8),
                        Text(
                          '操作指引',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '1. 点击下方按钮测试权限申请\n2. 首次申请时系统会弹出权限对话框\n3. 权限申请后会出现在iPhone设置中\n4. 如果权限被拒绝，需要删除重装应用',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.orange.shade800,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Container(
                      width: double.infinity,
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.delete_forever, color: Colors.red.shade700, size: 20),
                              const SizedBox(width: 6),
                              Text(
                                '权限被拒绝了？',
                                style: TextStyle(
                                  fontWeight: FontWeight.bold,
                                  color: Colors.red.shade700,
                                  fontSize: 14,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            '删除应用：长按应用图标 → 删除App → 重新运行',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.red.shade600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 20),
              
              // 权限状态显示
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '当前权限状态',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: [
                        Icon(
                          Icons.photo_library,
                          color: _photosPermission ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '相册权限: ${_photosPermission ? "已授权" : "未授权"}',
                          style: TextStyle(
                            color: _photosPermission ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.camera_alt,
                          color: _cameraPermission ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          '相机权限: ${_cameraPermission ? "已授权" : "未授权"}',
                          style: TextStyle(
                            color: _cameraPermission ? Colors.green : Colors.red,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    ElevatedButton(
                      onPressed: _checkPermissionStatus,
                      child: const Text('刷新权限状态'),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 手动权限测试按钮
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '手动权限测试',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    Column(
                      children: [
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton.icon(
                            onPressed: _testPhotosPermission,
                            icon: const Icon(Icons.photo_library, size: 24),
                            label: const Text('🏞️ 立即测试相册权限', style: TextStyle(fontSize: 16)),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.blue,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(height: 12),
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton.icon(
                            onPressed: _testCameraPermission,
                            icon: const Icon(Icons.camera_alt, size: 24),
                            label: const Text('📷 立即测试相机权限', style: TextStyle(fontSize: 16)),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.green,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 图片选择器测试
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '图片选择器测试',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SimpleImagePicker(
                      onImagesSelected: (images) {
                        setState(() {
                          _selectedImages = images;
                        });
                        print('选择的图片: $images');
                      },
                      maxImages: 3,
                      allowMultiple: true,
                      buttonText: '选择照片 (最多3张)',
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 20),
            
            // 调试信息显示
            if (_debugMessages.isNotEmpty) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.bug_report, color: Colors.purple),
                          const SizedBox(width: 8),
                          const Text(
                            '调试信息',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          TextButton(
                            onPressed: () {
                              setState(() {
                                _debugMessages.clear();
                              });
                            },
                            child: const Text('清除'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Container(
                        width: double.infinity,
                        height: 150,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.grey.shade300),
                        ),
                        child: ListView.builder(
                          itemCount: _debugMessages.length,
                          itemBuilder: (context, index) {
                            return Padding(
                              padding: const EdgeInsets.symmetric(vertical: 2),
                              child: Text(
                                _debugMessages[index],
                                style: const TextStyle(
                                  fontSize: 12,
                                  fontFamily: 'monospace',
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 20),
            ],
            
            // 选择的图片显示
            if (_selectedImages.isNotEmpty) ...[
              const Text(
                '已选择的图片:',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              SizedBox(
                height: 100,
                child: ListView.builder(
                  scrollDirection: Axis.horizontal,
                  itemCount: _selectedImages.length,
                  itemBuilder: (context, index) {
                    return Container(
                      margin: const EdgeInsets.only(right: 8),
                      width: 100,
                      height: 100,
                      decoration: BoxDecoration(
                        border: Border.all(color: Colors.grey),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: Image.file(
                          File(_selectedImages[index]),
                          fit: BoxFit.cover,
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 测试相册权限
  Future<void> _testPhotosPermission() async {
    _addDebugMessage('🏞️ 开始测试相册权限...');
    
    final hasPermission = await _permissionService.requestPhotosPermission(context);
    
    _addDebugMessage(hasPermission ? '✅ 相册权限获取成功!' : '❌ 相册权限获取失败!');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          hasPermission ? '相册权限获取成功!' : '相册权限获取失败!',
        ),
        backgroundColor: hasPermission ? Colors.green : Colors.red,
      ),
    );
    
    // 刷新权限状态
    await Future.delayed(const Duration(milliseconds: 500));
    _checkPermissionStatus();
  }

  /// 测试相机权限
  Future<void> _testCameraPermission() async {
    _addDebugMessage('📷 开始测试相机权限...');
    
    final hasPermission = await _permissionService.requestCameraPermission(context);
    
    _addDebugMessage(hasPermission ? '✅ 相机权限获取成功!' : '❌ 相机权限获取失败!');
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          hasPermission ? '相机权限获取成功!' : '相机权限获取失败!',
        ),
        backgroundColor: hasPermission ? Colors.green : Colors.red,
      ),
    );
    
    // 刷新权限状态
    await Future.delayed(const Duration(milliseconds: 500));
    _checkPermissionStatus();
  }
} 