import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';

/// 权限诊断页面
/// 专门用于诊断和解决权限问题
class PermissionDiagnosisScreen extends StatefulWidget {
  const PermissionDiagnosisScreen({super.key});

  @override
  State<PermissionDiagnosisScreen> createState() => _PermissionDiagnosisScreenState();
}

class _PermissionDiagnosisScreenState extends State<PermissionDiagnosisScreen> {
  final ImagePicker _picker = ImagePicker();
  Map<Permission, PermissionStatus> _permissionStatuses = {};
  bool _isLoading = false;
  String _diagnosisResult = '';
  List<String> _recommendations = [];

  @override
  void initState() {
    super.initState();
    _runDiagnosis();
  }

  /// 运行权限诊断
  Future<void> _runDiagnosis() async {
    setState(() {
      _isLoading = true;
      _diagnosisResult = '正在诊断权限配置...';
    });

    try {
      // 检查所有相关权限
      final permissions = [
        Permission.photos,
        Permission.camera,
        if (Platform.isAndroid) ...[
          Permission.storage,
          Permission.manageExternalStorage,
        ],
      ];

      final statuses = await permissions.request();
      
      setState(() {
        _permissionStatuses = statuses;
      });

      // 生成诊断结果
      _generateDiagnosisResult();
      
    } catch (e) {
      setState(() {
        _diagnosisResult = '诊断失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 生成诊断结果
  void _generateDiagnosisResult() {
    final results = <String>[];
    final recommendations = <String>[];

    // 检查相册权限
    final photosStatus = _permissionStatuses[Permission.photos];
    if (photosStatus != null) {
      if (photosStatus.isGranted || photosStatus.isLimited) {
        results.add('✅ 相册权限: 正常 (${photosStatus.name})');
      } else if (photosStatus.isPermanentlyDenied) {
        results.add('❌ 相册权限: 被永久拒绝');
        recommendations.add('需要手动到设置中开启相册权限');
      } else {
        results.add('⚠️ 相册权限: 未授权 (${photosStatus.name})');
        recommendations.add('需要重新申请相册权限');
      }
    }

    // 检查相机权限
    final cameraStatus = _permissionStatuses[Permission.camera];
    if (cameraStatus != null) {
      if (cameraStatus.isGranted) {
        results.add('✅ 相机权限: 正常');
      } else if (cameraStatus.isPermanentlyDenied) {
        results.add('❌ 相机权限: 被永久拒绝');
        recommendations.add('需要手动到设置中开启相机权限');
      } else {
        results.add('⚠️ 相机权限: 未授权 (${cameraStatus.name})');
        recommendations.add('需要重新申请相机权限');
      }
    }

    // Android特殊权限检查
    if (Platform.isAndroid) {
      final storageStatus = _permissionStatuses[Permission.storage];
      if (storageStatus != null && !storageStatus.isGranted) {
        results.add('⚠️ 存储权限: 未授权');
        recommendations.add('Android设备需要存储权限来访问照片');
      }
    }

    setState(() {
      _diagnosisResult = results.join('\n');
      _recommendations = recommendations;
    });
  }

  /// 一键修复权限
  Future<void> _fixPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 显示修复指导
      final shouldProceed = await _showFixGuidanceDialog();
      
      if (shouldProceed) {
        // 打开应用设置
        await openAppSettings();
        
        // 等待用户返回后重新诊断
        await Future.delayed(const Duration(seconds: 2));
        await _runDiagnosis();
      }
    } catch (e) {
      _showSnackBar('修复失败: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 显示修复指导对话框
  Future<bool> _showFixGuidanceDialog() async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.build, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              const Text('一键修复权限'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '即将打开系统设置，请按以下步骤操作：',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '修复步骤：',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text('1. 找到"Hera"应用'),
                    const Text('2. 开启"照片"权限'),
                    const Text('3. 开启"相机"权限'),
                    const Text('4. 返回应用查看结果'),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              if (_recommendations.isNotEmpty) ...[
                Text(
                  '建议操作：',
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade700,
                  ),
                ),
                const SizedBox(height: 4),
                ..._recommendations.map((rec) => Text('• $rec')),
              ],
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去设置'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 测试相册功能
  Future<void> _testPhotoLibrary() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        _showSnackBar('成功选择${images.length}张照片！', Colors.green);
      } else {
        _showSnackBar('未选择任何照片', Colors.grey);
      }
    } catch (e) {
      _showSnackBar('选择照片失败: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试相机功能
  Future<void> _testCamera() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        _showSnackBar('拍照成功！', Colors.green);
      } else {
        _showSnackBar('未拍摄照片', Colors.grey);
      }
    } catch (e) {
      _showSnackBar('拍照失败: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('权限诊断'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _runDiagnosis,
            tooltip: '重新诊断',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('正在诊断权限...'),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDiagnosisCard(),
                  const SizedBox(height: 20),
                  _buildFixCard(),
                  const SizedBox(height: 20),
                  _buildTestCard(),
                  const SizedBox(height: 20),
                  _buildTipsCard(),
                ],
              ),
            ),
    );
  }

  /// 构建诊断结果卡片
  Widget _buildDiagnosisCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medical_services, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                const Text(
                  '诊断结果',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                _diagnosisResult.isEmpty ? '等待诊断...' : _diagnosisResult,
                style: const TextStyle(
                  fontSize: 14,
                  fontFamily: 'monospace',
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建修复卡片
  Widget _buildFixCard() {
    final hasIssues = _recommendations.isNotEmpty;
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  hasIssues ? Icons.build : Icons.check_circle,
                  color: hasIssues ? Colors.orange : Colors.green,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  hasIssues ? '需要修复' : '权限正常',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            if (hasIssues) ...[
              SizedBox(
                width: double.infinity,
                height: 50,
                child: ElevatedButton.icon(
                  onPressed: _fixPermissions,
                  icon: const Icon(Icons.build),
                  label: const Text('一键修复权限'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.orange,
                    foregroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ] else ...[
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Text(
                  '✅ 所有权限配置正常，可以正常使用相册和相机功能',
                  style: TextStyle(color: Colors.green),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建测试卡片
  Widget _buildTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.science, color: Colors.purple, size: 24),
                const SizedBox(width: 8),
                const Text(
                  '功能测试',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: SizedBox(
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: _testPhotoLibrary,
                      icon: const Icon(Icons.photo_library),
                      label: const Text('测试相册'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: SizedBox(
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: _testCamera,
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('测试相机'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建提示卡片
  Widget _buildTipsCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.amber, size: 24),
                const SizedBox(width: 8),
                const Text(
                  '使用提示',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.amber.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text('💡 权限使用建议：'),
                  const SizedBox(height: 8),
                  const Text('• 首次使用时请允许访问"所有照片"'),
                  const Text('• 如果选择"选中的照片"，功能可能受限'),
                  const Text('• 权限被拒绝后可通过"一键修复"解决'),
                  const Text('• 建议定期检查权限状态'),
                  const SizedBox(height: 8),
                  Text(
                    '当前平台: ${Platform.isIOS ? "iOS" : "Android"}',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示提示消息
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
