import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_picker/image_picker.dart';

/// 权限诊断页面
/// 分析权限问题的根本原因并提供解决方案
class PermissionDiagnosisScreen extends StatefulWidget {
  const PermissionDiagnosisScreen({super.key});

  @override
  State<PermissionDiagnosisScreen> createState() => _PermissionDiagnosisScreenState();
}

class _PermissionDiagnosisScreenState extends State<PermissionDiagnosisScreen> {
  final ImagePicker _picker = ImagePicker();
  Map<String, PermissionStatus> _permissionStatus = {};
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _diagnosisPermissions();
  }

  /// 诊断权限状态
  Future<void> _diagnosisPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      final photos = await Permission.photos.status;
      final camera = await Permission.camera.status;
      
      setState(() {
        _permissionStatus = {
          'photos': photos,
          'camera': camera,
        };
      });
      
      print('🔍 权限诊断结果:');
      print('📸 相册权限: ${photos.name}');
      print('📷 相机权限: ${camera.name}');
      
    } catch (e) {
      print('❌ 权限诊断失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('权限诊断'),
        backgroundColor: Colors.teal,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _diagnosisPermissions,
            tooltip: '重新诊断',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(20.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildDiagnosisCard(),
                  const SizedBox(height: 20),
                  _buildSolutionCard(),
                  const SizedBox(height: 20),
                  _buildStepsCard(),
                ],
              ),
            ),
    );
  }

  /// 构建诊断结果卡片
  Widget _buildDiagnosisCard() {
    final photosStatus = _permissionStatus['photos'];
    final cameraStatus = _permissionStatus['camera'];
    
    return Card(
      color: Colors.teal.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.medical_services, color: Colors.teal.shade700),
                const SizedBox(width: 8),
                Text(
                  '权限诊断结果',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.teal.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 相册权限状态
            _buildPermissionStatusTile(
              '相册权限',
              photosStatus,
              Icons.photo_library,
              Colors.blue,
            ),
            const SizedBox(height: 12),
            
            // 相机权限状态
            _buildPermissionStatusTile(
              '相机权限',
              cameraStatus,
              Icons.camera_alt,
              Colors.green,
            ),
            
            const SizedBox(height: 16),
            
            // 问题分析
            _buildProblemAnalysis(photosStatus, cameraStatus),
          ],
        ),
      ),
    );
  }

  /// 构建权限状态项
  Widget _buildPermissionStatusTile(
    String name,
    PermissionStatus? status,
    IconData icon,
    Color color,
  ) {
    final statusText = status?.name ?? '未知';
    final statusColor = _getStatusColor(status);
    final statusIcon = _getStatusIcon(status);
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: statusColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: statusColor.withOpacity(0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  _getStatusDescription(status),
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: statusColor,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(statusIcon, color: Colors.white, size: 16),
                const SizedBox(width: 4),
                Text(
                  statusText,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建问题分析
  Widget _buildProblemAnalysis(
    PermissionStatus? photosStatus,
    PermissionStatus? cameraStatus,
  ) {
    final hasPermanentlyDenied = photosStatus == PermissionStatus.permanentlyDenied || 
                                cameraStatus == PermissionStatus.permanentlyDenied;
    
    String analysisText;
    Color analysisColor;
    IconData analysisIcon;
    
    if (hasPermanentlyDenied) {
      analysisText = '🚨 检测到严重问题：权限被永久拒绝\n'
                    'iOS系统已缓存权限拒绝状态，无法通过代码重置。\n'
                    '这就是为什么设置中没有显示权限选项的原因。';
      analysisColor = Colors.red;
      analysisIcon = Icons.error;
    } else if (photosStatus == PermissionStatus.denied || 
               cameraStatus == PermissionStatus.denied) {
      analysisText = '⚠️ 权限被拒绝\n'
                    '权限处于拒绝状态，需要重新申请。';
      analysisColor = Colors.orange;
      analysisIcon = Icons.warning;
    } else if (photosStatus == PermissionStatus.granted && 
               cameraStatus == PermissionStatus.granted) {
      analysisText = '✅ 权限状态正常\n'
                    '所有权限都已正确授予。';
      analysisColor = Colors.green;
      analysisIcon = Icons.check_circle;
    } else {
      analysisText = '❓ 权限状态混合\n'
                    '部分权限正常，部分权限有问题。';
      analysisColor = Colors.grey;
      analysisIcon = Icons.help;
    }
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: analysisColor.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: analysisColor.withOpacity(0.3)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(analysisIcon, color: analysisColor, size: 24),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              analysisText,
              style: TextStyle(
                fontSize: 14,
                height: 1.4,
                color: analysisColor.withOpacity(0.8),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建解决方案卡片
  Widget _buildSolutionCard() {
    final photosStatus = _permissionStatus['photos'];
    final cameraStatus = _permissionStatus['camera'];
    final hasPermanentlyDenied = photosStatus == PermissionStatus.permanentlyDenied || 
                                cameraStatus == PermissionStatus.permanentlyDenied;
    
    return Card(
      color: Colors.orange.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.lightbulb, color: Colors.orange.shade700),
                const SizedBox(width: 8),
                Text(
                  '解决方案',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.orange.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            if (hasPermanentlyDenied) ...[
              _buildSolutionItem(
                '🗑️ 完全删除应用',
                '这是唯一能重置iOS权限缓存的方法',
                Colors.red,
                isRecommended: true,
              ),
              const SizedBox(height: 12),
              _buildSolutionItem(
                '📱 重新安装应用',
                '删除后重新安装，权限状态会完全重置',
                Colors.blue,
                isRecommended: true,
              ),
            ] else ...[
              _buildSolutionItem(
                '🔄 重新申请权限',
                '权限状态正常，可以尝试重新申请',
                Colors.green,
                isRecommended: true,
              ),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建解决方案项
  Widget _buildSolutionItem(
    String title,
    String description,
    Color color, {
    bool isRecommended = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: isRecommended ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            isRecommended ? Icons.star : Icons.circle,
            color: color,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                    if (isRecommended) ...[
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                        decoration: BoxDecoration(
                          color: Colors.amber,
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: const Text(
                          '推荐',
                          style: TextStyle(
                            fontSize: 10,
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ],
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建操作步骤卡片
  Widget _buildStepsCard() {
    return Card(
      color: Colors.blue.shade50,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.list_alt, color: Colors.blue.shade700),
                const SizedBox(width: 8),
                Text(
                  '详细操作步骤',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            _buildStepItem(1, '长按Hera应用图标', '在iPhone主屏幕上找到应用'),
            _buildStepItem(2, '选择"删除App"', '点击弹出菜单中的删除选项'),
            _buildStepItem(3, '确认删除', '确保应用完全从设备中移除'),
            _buildStepItem(4, '重新安装应用', '从App Store或开发工具重新安装'),
            _buildStepItem(5, '测试权限申请', '使用清洁权限测试页面验证'),
            
            const SizedBox(height: 16),
            
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.green.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.check_circle, color: Colors.green.shade700),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      '重新安装后，首次申请权限时会弹出系统对话框，选择"允许"后就会在设置中显示权限选项。',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.green.shade700,
                        height: 1.4,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建步骤项
  Widget _buildStepItem(int step, String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 28,
            height: 28,
            decoration: BoxDecoration(
              color: Colors.blue,
              borderRadius: BorderRadius.circular(14),
            ),
            child: Center(
              child: Text(
                '$step',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 16,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取状态颜色
  Color _getStatusColor(PermissionStatus? status) {
    switch (status) {
      case PermissionStatus.granted:
        return Colors.green;
      case PermissionStatus.limited:
        return Colors.orange;
      case PermissionStatus.denied:
        return Colors.red;
      case PermissionStatus.permanentlyDenied:
        return Colors.red.shade800;
      case PermissionStatus.restricted:
        return Colors.grey;
      default:
        return Colors.grey;
    }
  }

  /// 获取状态图标
  IconData _getStatusIcon(PermissionStatus? status) {
    switch (status) {
      case PermissionStatus.granted:
        return Icons.check;
      case PermissionStatus.limited:
        return Icons.remove;
      case PermissionStatus.denied:
        return Icons.close;
      case PermissionStatus.permanentlyDenied:
        return Icons.block;
      case PermissionStatus.restricted:
        return Icons.warning;
      default:
        return Icons.help;
    }
  }

  /// 获取状态描述
  String _getStatusDescription(PermissionStatus? status) {
    switch (status) {
      case PermissionStatus.granted:
        return '已授权，可以正常使用';
      case PermissionStatus.limited:
        return '有限访问，部分功能可用';
      case PermissionStatus.denied:
        return '被拒绝，需要重新申请';
      case PermissionStatus.permanentlyDenied:
        return '永久拒绝，需要删除重装';
      case PermissionStatus.restricted:
        return '系统限制，可能被家长控制';
      default:
        return '未知状态';
    }
  }
}
