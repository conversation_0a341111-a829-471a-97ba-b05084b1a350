import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 清洁权限测试页面
/// 使用最简单的方法测试权限申请，确保iOS系统能正确显示权限对话框
class CleanPermissionTestScreen extends StatefulWidget {
  const CleanPermissionTestScreen({super.key});

  @override
  State<CleanPermissionTestScreen> createState() => _CleanPermissionTestScreenState();
}

class _CleanPermissionTestScreenState extends State<CleanPermissionTestScreen> {
  String _status = '准备测试权限...';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('清洁权限测试'),
        backgroundColor: Colors.green,
        foregroundColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 说明卡片
            Card(
              color: Colors.green.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.cleaning_services, color: Colors.green.shade700),
                        const SizedBox(width: 8),
                        Text(
                          '清洁权限测试',
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    const Text(
                      '这个测试使用最简单直接的方法申请权限，确保iOS系统能够正确显示权限对话框并在设置中添加权限选项。',
                      style: TextStyle(fontSize: 14, height: 1.4),
                    ),
                  ],
                ),
              ),
            ),
            
            const SizedBox(height: 30),
            
            // 状态显示
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    '测试状态：',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _status,
                    style: const TextStyle(fontSize: 16),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 30),
            
            // 测试按钮
            Column(
              children: [
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testPhotosPermissionClean,
                    icon: _isLoading 
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                          )
                        : const Icon(Icons.photo_library, size: 28),
                    label: const Text(
                      '🏞️ 测试相册权限 (最简洁)',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                ),
                
                const SizedBox(height: 20),
                
                SizedBox(
                  width: double.infinity,
                  height: 60,
                  child: ElevatedButton.icon(
                    onPressed: _isLoading ? null : _testCameraPermissionClean,
                    icon: _isLoading 
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2, color: Colors.white),
                          )
                        : const Icon(Icons.camera_alt, size: 28),
                    label: const Text(
                      '📷 测试相机权限 (最简洁)',
                      style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                    ),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(15),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 30),
            
            // 说明文字
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info, color: Colors.orange.shade700),
                      const SizedBox(width: 8),
                      Text(
                        '预期效果',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: Colors.orange.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '• 点击按钮后会直接弹出iOS系统权限对话框\n'
                    '• 选择"允许"后，在iPhone设置→Hera中会出现对应权限选项\n'
                    '• 如果没有弹出对话框，说明权限状态有问题，需要删除重装应用',
                    style: TextStyle(fontSize: 14, height: 1.4),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 最简洁的相册权限测试
  Future<void> _testPhotosPermissionClean() async {
    setState(() {
      _isLoading = true;
      _status = '🏞️ 正在测试相册权限...';
    });

    try {
      // 1. 检查当前状态
      final currentStatus = await Permission.photos.status;
      print('📋 当前相册权限状态: ${currentStatus.name}');
      
      setState(() {
        _status = '📋 当前状态: ${currentStatus.name}\n正在请求权限...';
      });

      // 2. 直接请求权限（最简单的方式）
      final result = await Permission.photos.request();
      print('📝 相册权限请求结果: ${result.name}');

      // 3. 显示结果
      String resultMessage;
      Color resultColor;
      
      if (result.isGranted) {
        resultMessage = '✅ 相册权限申请成功！\n现在可以在iPhone设置→Hera中看到相册权限选项';
        resultColor = Colors.green;
      } else if (result.isLimited) {
        resultMessage = '🔒 相册权限获得有限访问！\n现在可以在iPhone设置→Hera中看到相册权限选项';
        resultColor = Colors.orange;
      } else if (result.isDenied) {
        resultMessage = '❌ 相册权限被拒绝\n用户主动拒绝了权限申请';
        resultColor = Colors.red;
      } else if (result.isPermanentlyDenied) {
        resultMessage = '🚫 相册权限被永久拒绝\n需要删除应用重新安装';
        resultColor = Colors.red;
      } else {
        resultMessage = '❓ 未知状态: ${result.name}';
        resultColor = Colors.grey;
      }

      setState(() {
        _status = resultMessage;
      });

      // 4. 显示结果 SnackBar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(resultMessage),
            backgroundColor: resultColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }

    } catch (e) {
      final errorMessage = '💥 权限测试出错: $e';
      setState(() {
        _status = errorMessage;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 最简洁的相机权限测试
  Future<void> _testCameraPermissionClean() async {
    setState(() {
      _isLoading = true;
      _status = '📷 正在测试相机权限...';
    });

    try {
      // 1. 检查当前状态
      final currentStatus = await Permission.camera.status;
      print('📋 当前相机权限状态: ${currentStatus.name}');
      
      setState(() {
        _status = '📋 当前状态: ${currentStatus.name}\n正在请求权限...';
      });

      // 2. 直接请求权限（最简单的方式）
      final result = await Permission.camera.request();
      print('📝 相机权限请求结果: ${result.name}');

      // 3. 显示结果
      String resultMessage;
      Color resultColor;
      
      if (result.isGranted) {
        resultMessage = '✅ 相机权限申请成功！\n现在可以在iPhone设置→Hera中看到相机权限选项';
        resultColor = Colors.green;
      } else if (result.isDenied) {
        resultMessage = '❌ 相机权限被拒绝\n用户主动拒绝了权限申请';
        resultColor = Colors.red;
      } else if (result.isPermanentlyDenied) {
        resultMessage = '🚫 相机权限被永久拒绝\n需要删除应用重新安装';
        resultColor = Colors.red;
      } else {
        resultMessage = '❓ 未知状态: ${result.name}';
        resultColor = Colors.grey;
      }

      setState(() {
        _status = resultMessage;
      });

      // 4. 显示结果 SnackBar
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(resultMessage),
            backgroundColor: resultColor,
            duration: const Duration(seconds: 3),
          ),
        );
      }

    } catch (e) {
      final errorMessage = '💥 权限测试出错: $e';
      setState(() {
        _status = errorMessage;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(errorMessage),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}
