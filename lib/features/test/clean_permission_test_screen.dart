import 'package:flutter/material.dart';
import '../../core/services/clean_permission_manager.dart';
import '../../shared/widgets/clean_image_picker.dart';
import '../settings/permission_settings_screen.dart';

/// 清洁权限测试页面
/// 测试新的权限管理系统
class CleanPermissionTestScreen extends StatefulWidget {
  const CleanPermissionTestScreen({super.key});

  @override
  State<CleanPermissionTestScreen> createState() => _CleanPermissionTestScreenState();
}

class _CleanPermissionTestScreenState extends State<CleanPermissionTestScreen> {
  final CleanPermissionManager _permissionManager = CleanPermissionManager();
  Map<String, bool> _permissionStatus = {};
  List<String> _selectedImages = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _checkPermissions();
  }

  /// 检查权限状态
  Future<void> _checkPermissions() async {
    setState(() => _isLoading = true);
    
    try {
      final status = await _permissionManager.checkPermissionStatus();
      setState(() => _permissionStatus = status);
    } catch (e) {
      print('检查权限状态失败: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('清洁权限测试'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const PermissionSettingsScreen(),
                ),
              ).then((_) => _checkPermissions());
            },
            tooltip: '权限设置',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkPermissions,
            tooltip: '刷新状态',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildStatusCard(),
                  const SizedBox(height: 20),
                  _buildQuickTestCard(),
                  const SizedBox(height: 20),
                  _buildImagePickerCard(),
                  const SizedBox(height: 20),
                  if (_selectedImages.isNotEmpty) _buildSelectedImagesCard(),
                ],
              ),
            ),
    );
  }

  /// 构建状态卡片
  Widget _buildStatusCard() {
    final photosGranted = _permissionStatus['photos'] ?? false;
    final cameraGranted = _permissionStatus['camera'] ?? false;

    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.security, color: Colors.blue, size: 24),
                const SizedBox(width: 8),
                const Text(
                  '权限状态',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // 相册权限状态
            _buildPermissionStatusRow(
              '相册权限',
              photosGranted,
              Icons.photo_library,
              Colors.blue,
            ),
            const SizedBox(height: 12),
            
            // 相机权限状态
            _buildPermissionStatusRow(
              '相机权限',
              cameraGranted,
              Icons.camera_alt,
              Colors.green,
            ),
            
            const SizedBox(height: 16),
            
            // 整体状态
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (photosGranted && cameraGranted)
                    ? Colors.green.withOpacity(0.1)
                    : Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    (photosGranted && cameraGranted) ? Icons.check_circle : Icons.warning,
                    color: (photosGranted && cameraGranted) ? Colors.green : Colors.orange,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      (photosGranted && cameraGranted)
                          ? '所有权限已配置，可以正常使用功能'
                          : '部分权限未配置，可能影响功能使用',
                      style: TextStyle(
                        color: (photosGranted && cameraGranted) ? Colors.green : Colors.orange,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建权限状态行
  Widget _buildPermissionStatusRow(
    String name,
    bool isGranted,
    IconData icon,
    Color color,
  ) {
    return Row(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(width: 8),
        Expanded(child: Text(name)),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: isGranted ? Colors.green.withOpacity(0.1) : Colors.red.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Text(
            isGranted ? '已开启' : '未开启',
            style: TextStyle(
              fontSize: 12,
              color: isGranted ? Colors.green : Colors.red,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  /// 构建快速测试卡片
  Widget _buildQuickTestCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.flash_on, color: Colors.orange, size: 24),
                const SizedBox(width: 8),
                const Text(
                  '快速测试',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testPhotosPermission,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('测试相册权限'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _testCameraPermission,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('测试相机权限'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _testAllPermissions,
                icon: const Icon(Icons.security),
                label: const Text('测试所有权限'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Colors.purple,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建图片选择器卡片
  Widget _buildImagePickerCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.image, color: Colors.indigo, size: 24),
                const SizedBox(width: 8),
                const Text(
                  '图片选择器测试',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            CleanImagePicker(
              onImagesSelected: (images) {
                setState(() {
                  _selectedImages = images;
                });
                _showSnackBar('成功选择${images.length}张图片', Colors.green);
              },
              maxImages: 3,
              allowMultiple: true,
              buttonText: '选择图片 (最多3张)',
              buttonColor: Colors.indigo,
              buttonIcon: Icons.add_photo_alternate,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建已选择图片卡片
  Widget _buildSelectedImagesCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.check_circle, color: Colors.green, size: 24),
                const SizedBox(width: 8),
                Text(
                  '已选择的图片 (${_selectedImages.length})',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 100,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      color: Colors.grey.shade200,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: const Icon(
                      Icons.image,
                      color: Colors.grey,
                      size: 40,
                    ),
                  );
                },
              ),
            ),
            
            const SizedBox(height: 12),
            
            SizedBox(
              width: double.infinity,
              child: TextButton.icon(
                onPressed: () {
                  setState(() {
                    _selectedImages.clear();
                  });
                },
                icon: const Icon(Icons.clear),
                label: const Text('清空选择'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 测试相册权限
  Future<void> _testPhotosPermission() async {
    final result = await _permissionManager.requestPhotosPermission(context);
    _showSnackBar(
      result ? '相册权限获取成功' : '相册权限获取失败',
      result ? Colors.green : Colors.red,
    );
    await _checkPermissions();
  }

  /// 测试相机权限
  Future<void> _testCameraPermission() async {
    final result = await _permissionManager.requestCameraPermission(context);
    _showSnackBar(
      result ? '相机权限获取成功' : '相机权限获取失败',
      result ? Colors.green : Colors.red,
    );
    await _checkPermissions();
  }

  /// 测试所有权限
  Future<void> _testAllPermissions() async {
    final results = await _permissionManager.requestImagePermissions(context);
    final allGranted = results.values.every((granted) => granted);
    
    _showSnackBar(
      allGranted ? '所有权限获取成功' : '部分权限获取失败',
      allGranted ? Colors.green : Colors.orange,
    );
    await _checkPermissions();
  }

  /// 显示提示消息
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
