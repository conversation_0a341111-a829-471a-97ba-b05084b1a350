import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import '../../core/services/permission_reset_service.dart';

/// 权限重置测试页面
/// 专门用于测试和解决权限被永久拒绝的问题
class PermissionResetTestScreen extends StatefulWidget {
  const PermissionResetTestScreen({super.key});

  @override
  State<PermissionResetTestScreen> createState() => _PermissionResetTestScreenState();
}

class _PermissionResetTestScreenState extends State<PermissionResetTestScreen> {
  final PermissionResetService _resetService = PermissionResetService();
  final ImagePicker _picker = ImagePicker();
  bool _hasPhotosPermission = false;
  bool _hasCameraPermission = false;
  bool _isLoading = false;
  String _statusMessage = '检查中...';
  List<String> _selectedImages = [];

  @override
  void initState() {
    super.initState();
    _checkPermissionStatus();
  }

  /// 检查权限状态
  Future<void> _checkPermissionStatus() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在检查权限状态...';
    });

    try {
      final hasPhotos = await _resetService.checkPhotosPermission();
      final hasCamera = await _resetService.checkCameraPermission();
      setState(() {
        _hasPhotosPermission = hasPhotos;
        _hasCameraPermission = hasCamera;
        _statusMessage = '相册: ${hasPhotos ? "已授权" : "未授权"}, 相机: ${hasCamera ? "已授权" : "未授权"}';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '检查失败: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 强制重置相册权限
  Future<void> _forceResetPhotosPermission() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在重置相册权限...';
    });

    try {
      final success = await _resetService.forceResetPhotosPermission(context);

      if (success) {
        setState(() {
          _hasPhotosPermission = true;
          _statusMessage = '相册权限重置成功！';
        });
        _showSnackBar('相册权限重置成功，现在可以选择照片了！', Colors.green);
      } else {
        setState(() {
          _statusMessage = '相册权限重置失败';
        });
        _showSnackBar('相册权限重置失败，请重试', Colors.red);
      }
    } catch (e) {
      setState(() {
        _statusMessage = '重置失败: $e';
      });
      _showSnackBar('重置过程中发生错误', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });

      // 重新检查权限状态
      await _checkPermissionStatus();
    }
  }

  /// 强制重置相机权限
  Future<void> _forceResetCameraPermission() async {
    setState(() {
      _isLoading = true;
      _statusMessage = '正在重置相机权限...';
    });

    try {
      final success = await _resetService.forceResetCameraPermission(context);

      if (success) {
        setState(() {
          _hasCameraPermission = true;
          _statusMessage = '相机权限重置成功！';
        });
        _showSnackBar('相机权限重置成功，现在可以拍照了！', Colors.green);
      } else {
        setState(() {
          _statusMessage = '相机权限重置失败';
        });
        _showSnackBar('相机权限重置失败，请重试', Colors.red);
      }
    } catch (e) {
      setState(() {
        _statusMessage = '重置失败: $e';
      });
      _showSnackBar('重置过程中发生错误', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });

      // 重新检查权限状态
      await _checkPermissionStatus();
    }
  }

  /// 测试选择照片
  Future<void> _testSelectPhotos() async {
    if (!_hasPhotosPermission) {
      _showSnackBar('请先获取相册权限', Colors.orange);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final images = await _picker.pickMultiImage(
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (images.isNotEmpty) {
        setState(() {
          _selectedImages = images.map((image) => image.path).toList();
        });
        _showSnackBar('成功选择${images.length}张照片！', Colors.green);
      } else {
        _showSnackBar('未选择任何照片', Colors.grey);
      }
    } catch (e) {
      _showSnackBar('选择照片失败: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 测试拍照
  Future<void> _testTakePhoto() async {
    if (!_hasCameraPermission) {
      _showSnackBar('请先获取相机权限', Colors.orange);
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final image = await _picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1920,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImages = [image.path];
        });
        _showSnackBar('拍照成功！', Colors.green);
      } else {
        _showSnackBar('未拍摄照片', Colors.grey);
      }
    } catch (e) {
      _showSnackBar('拍照失败: $e', Colors.red);
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('权限重置测试'),
        backgroundColor: Colors.orange,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _checkPermissionStatus,
            tooltip: '刷新权限状态',
          ),
        ],
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildStatusCard(),
            const SizedBox(height: 20),
            _buildActionButtons(),
            const SizedBox(height: 20),
            _buildTestSection(),
            const SizedBox(height: 20),
            if (_selectedImages.isNotEmpty) _buildSelectedImagesSection(),
          ],
        ),
      ),
    );
  }

  /// 构建状态卡片
  Widget _buildStatusCard() {
    return Card(
      elevation: 4,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  (_hasPhotosPermission && _hasCameraPermission) ? Icons.check_circle : Icons.error,
                  color: (_hasPhotosPermission && _hasCameraPermission) ? Colors.green : Colors.red,
                  size: 24,
                ),
                const SizedBox(width: 8),
                const Text(
                  '权限状态',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: (_hasPhotosPermission && _hasCameraPermission)
                    ? Colors.green.withOpacity(0.1)
                    : Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '相册权限: ${_hasPhotosPermission ? "已授权" : "未授权"}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _hasPhotosPermission ? Colors.green : Colors.red,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    '相机权限: ${_hasCameraPermission ? "已授权" : "未授权"}',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: _hasCameraPermission ? Colors.green : Colors.red,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _statusMessage,
                    style: const TextStyle(fontSize: 14),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '权限操作',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: SizedBox(
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _forceResetPhotosPermission,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.photo_library),
                      label: Text(_isLoading ? '处理中...' : '重置相册权限'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.blue,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: SizedBox(
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _forceResetCameraPermission,
                      icon: _isLoading
                          ? const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(strokeWidth: 2),
                            )
                          : const Icon(Icons.camera_alt),
                      label: Text(_isLoading ? '处理中...' : '重置相机权限'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.green,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.orange.withOpacity(0.1),
                borderRadius: BorderRadius.circular(6),
              ),
              child: const Text(
                '💡 这个按钮会智能处理权限被永久拒绝的情况，并引导您完成权限重置',
                style: TextStyle(fontSize: 12, color: Colors.orange),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建测试区域
  Widget _buildTestSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '功能测试',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: SizedBox(
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _testSelectPhotos,
                      icon: const Icon(Icons.photo_library),
                      label: const Text('测试选择照片'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _hasPhotosPermission ? Colors.blue : Colors.grey,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: SizedBox(
                    height: 50,
                    child: ElevatedButton.icon(
                      onPressed: _isLoading ? null : _testTakePhoto,
                      icon: const Icon(Icons.camera_alt),
                      label: const Text('测试拍照'),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: _hasCameraPermission ? Colors.green : Colors.grey,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '相册: ${_hasPhotosPermission ? "✅ 可测试" : "❌ 需权限"} | 相机: ${_hasCameraPermission ? "✅ 可测试" : "❌ 需权限"}',
              style: const TextStyle(fontSize: 12, color: Colors.grey),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建已选择图片区域
  Widget _buildSelectedImagesSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '已选择的照片 (${_selectedImages.length})',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            SizedBox(
              height: 100,
              child: ListView.builder(
                scrollDirection: Axis.horizontal,
                itemCount: _selectedImages.length,
                itemBuilder: (context, index) {
                  return Container(
                    width: 100,
                    margin: const EdgeInsets.only(right: 8),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.grey.shade300),
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: Container(
                        color: Colors.grey.shade200,
                        child: const Icon(
                          Icons.image,
                          color: Colors.grey,
                          size: 40,
                        ),
                      ),
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 显示提示消息
  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }
}
