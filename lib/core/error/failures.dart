import 'package:freezed_annotation/freezed_annotation.dart';

part 'failures.freezed.dart';

/// 基础失败类
@freezed
class Failure with _$Failure {
  /// 服务器错误
  const factory Failure.server(String message) = ServerFailure;
  
  /// 网络错误
  const factory Failure.network(String message) = NetworkFailure;
  
  /// 缓存错误
  const factory Failure.cache(String message) = CacheFailure;
  
  /// 本地存储错误
  const factory Failure.localStorage(String message) = LocalStorageFailure;
  
  /// 验证错误
  const factory Failure.validation(String message) = ValidationFailure;
  
  /// 权限错误
  const factory Failure.permission(String message) = PermissionFailure;
  
  /// 取消操作
  const factory Failure.cancelled(String message) = CancelledFailure;
  
  /// 未知错误
  const factory Failure.unknown(String message) = UnknownFailure;
}

/// 扩展方法获取错误消息
extension FailureX on Failure {
  String get message {
    return when(
      server: (message) => message,
      network: (message) => message,
      cache: (message) => message,
      localStorage: (message) => message,
      validation: (message) => message,
      permission: (message) => message,
      cancelled: (message) => message,
      unknown: (message) => message,
    );
  }
}
