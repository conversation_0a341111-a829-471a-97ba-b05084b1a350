import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 优化的权限服务
/// 专门解决权限被永久拒绝的问题，提供更好的用户体验
class OptimizedPermissionService {
  static final OptimizedPermissionService _instance = OptimizedPermissionService._internal();
  factory OptimizedPermissionService() => _instance;
  OptimizedPermissionService._internal();

  /// 智能请求相册权限
  /// 根据平台和权限状态提供最佳的处理方案
  Future<bool> requestPhotosPermission(BuildContext context) async {
    try {
      print('🏞️ 开始智能相册权限申请');
      
      // 根据平台选择合适的权限
      Permission targetPermission = _getPhotosPermission();
      
      // 检查当前权限状态
      PermissionStatus status = await targetPermission.status;
      print('📋 当前相册权限状态: ${status.name}');
      print('📋 状态详细信息: granted=${status.isGranted}, limited=${status.isLimited}, denied=${status.isDenied}, permanentlyDenied=${status.isPermanentlyDenied}');
      
      // 如果已经有权限，直接返回true
      if (status.isGranted || status.isLimited) {
        print('✅ 相册权限已获得 (${status.name})');
        return true;
      }
      
      // 如果被永久拒绝，引导用户到设置
      if (status.isPermanentlyDenied) {
        print('❌ 相册权限被永久拒绝，引导用户到设置');
        if (context.mounted) {
          return await _handlePermanentlyDeniedPermission(
            context, 
            '相册权限', 
            '选择照片',
            Icons.photo_library,
            Colors.blue,
          );
        }
        return false;
      }
      
      // 显示权限说明对话框
      if (context.mounted) {
        print('💬 显示相册权限说明对话框');
        final shouldRequest = await _showPermissionRationaleDialog(
          context,
          '相册权限',
          '为了让您选择照片生成AI婚纱照，我们需要访问您的相册。',
          '• 只在您主动选择时访问相册\n• 不会上传或分享您的照片\n• 严格保护您的隐私',
          Icons.photo_library,
          Colors.blue,
        );
        
        if (!shouldRequest) {
          print('❌ 用户拒绝授权相册权限');
          return false;
        }
      }
      
      // 请求权限
      print('🔄 正在请求相册权限...');
      final result = await targetPermission.request();
      print('📝 相册权限请求结果: ${result.name}');
      print('📝 结果详细信息: granted=${result.isGranted}, limited=${result.isLimited}, denied=${result.isDenied}, permanentlyDenied=${result.isPermanentlyDenied}');
      
      // 检查结果
      if (result.isGranted || result.isLimited) {
        print('✅ 相册权限申请成功');
        if (context.mounted) {
          _showSuccessSnackBar(context, '相册权限已开启');
        }
        return true;
      } else if (result.isPermanentlyDenied && context.mounted) {
        print('❌ 相册权限被永久拒绝，引导用户到设置');
        return await _handlePermanentlyDeniedPermission(
          context, 
          '相册权限', 
          '选择照片',
          Icons.photo_library,
          Colors.blue,
        );
      } else {
        print('⚠️ 相册权限申请被拒绝');
        if (context.mounted) {
          _showErrorSnackBar(context, '相册权限被拒绝，无法选择照片');
        }
        return false;
      }
    } catch (e) {
      print('💥 请求相册权限时发生错误: $e');
      if (context.mounted) {
        _showErrorSnackBar(context, '权限申请失败，请重试');
      }
      return false;
    }
  }

  /// 智能请求相机权限
  Future<bool> requestCameraPermission(BuildContext context) async {
    try {
      print('📷 开始智能相机权限申请');
      
      // 检查当前权限状态
      PermissionStatus status = await Permission.camera.status;
      print('📋 当前相机权限状态: ${status.name}');
      print('📋 状态详细信息: granted=${status.isGranted}, denied=${status.isDenied}, permanentlyDenied=${status.isPermanentlyDenied}');
      
      // 如果已经有权限，直接返回true
      if (status.isGranted) {
        print('✅ 相机权限已获得');
        return true;
      }
      
      // 如果被永久拒绝，引导用户到设置
      if (status.isPermanentlyDenied) {
        print('❌ 相机权限被永久拒绝，引导用户到设置');
        if (context.mounted) {
          return await _handlePermanentlyDeniedPermission(
            context, 
            '相机权限', 
            '拍照',
            Icons.camera_alt,
            Colors.green,
          );
        }
        return false;
      }
      
      // 显示权限说明对话框
      if (context.mounted) {
        print('💬 显示相机权限说明对话框');
        final shouldRequest = await _showPermissionRationaleDialog(
          context,
          '相机权限',
          '为了让您拍摄照片生成AI婚纱照，我们需要访问您的相机。',
          '• 只在您主动拍照时使用相机\n• 不会在后台偷拍或录制\n• 严格保护您的隐私',
          Icons.camera_alt,
          Colors.green,
        );
        
        if (!shouldRequest) {
          print('❌ 用户拒绝授权相机权限');
          return false;
        }
      }
      
      // 请求权限
      print('🔄 正在请求相机权限...');
      final result = await Permission.camera.request();
      print('📝 相机权限请求结果: ${result.name}');
      print('📝 结果详细信息: granted=${result.isGranted}, denied=${result.isDenied}, permanentlyDenied=${result.isPermanentlyDenied}');
      
      // 检查结果
      if (result.isGranted) {
        print('✅ 相机权限申请成功');
        if (context.mounted) {
          _showSuccessSnackBar(context, '相机权限已开启');
        }
        return true;
      } else if (result.isPermanentlyDenied && context.mounted) {
        print('❌ 相机权限被永久拒绝，引导用户到设置');
        return await _handlePermanentlyDeniedPermission(
          context, 
          '相机权限', 
          '拍照',
          Icons.camera_alt,
          Colors.green,
        );
      } else {
        print('⚠️ 相机权限申请被拒绝');
        if (context.mounted) {
          _showErrorSnackBar(context, '相机权限被拒绝，无法拍照');
        }
        return false;
      }
    } catch (e) {
      print('💥 请求相机权限时发生错误: $e');
      if (context.mounted) {
        _showErrorSnackBar(context, '权限申请失败，请重试');
      }
      return false;
    }
  }

  /// 根据平台获取相册权限
  Permission _getPhotosPermission() {
    if (Platform.isIOS) {
      return Permission.photos;
    } else {
      // Android 13+ 推荐使用 photos 权限
      return Permission.photos;
    }
  }

  /// 处理被永久拒绝的权限
  Future<bool> _handlePermanentlyDeniedPermission(
    BuildContext context,
    String permissionName,
    String functionality,
    IconData icon,
    Color color,
  ) async {
    final result = await _showPermanentlyDeniedDialog(
      context,
      permissionName,
      functionality,
      icon,
      color,
    );
    
    if (result == true) {
      // 用户选择去设置，打开应用设置
      await openAppSettings();
      return false; // 返回false，因为需要用户手动设置
    }
    
    return false;
  }

  /// 显示权限说明对话框
  Future<bool> _showPermissionRationaleDialog(
    BuildContext context,
    String title,
    String description,
    String promises,
    IconData icon,
    Color color,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Expanded(child: Text(title, style: const TextStyle(fontSize: 18))),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                description,
                style: const TextStyle(fontSize: 16, height: 1.4),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '我们的承诺：',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      promises,
                      style: const TextStyle(fontSize: 14, height: 1.3),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('暂不开启', style: TextStyle(color: Colors.grey)),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('开启权限'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示权限被永久拒绝的对话框
  Future<bool> _showPermanentlyDeniedDialog(
    BuildContext context,
    String permissionName,
    String functionality,
    IconData icon,
    Color color,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.settings, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              Text('需要开启$permissionName'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '$permissionName已被拒绝，无法$functionality。请按以下步骤手动开启：',
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.settings, color: Colors.orange, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '设置步骤：',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.orange.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text('1. 点击"去设置"按钮'),
                    const Text('2. 找到并点击"Hera"应用'),
                    Text('3. 开启$permissionName'),
                    const Text('4. 返回应用重新尝试'),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去设置'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示成功提示
  void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 检查相册权限状态
  Future<bool> hasPhotosPermission() async {
    try {
      final permission = _getPhotosPermission();
      final status = await permission.status;
      return status.isGranted || status.isLimited;
    } catch (e) {
      print('检查相册权限时发生错误: $e');
      return false;
    }
  }

  /// 检查相机权限状态
  Future<bool> hasCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      print('检查相机权限时发生错误: $e');
      return false;
    }
  }

  /// 批量检查权限状态
  Future<Map<String, bool>> checkAllPermissions() async {
    final results = <String, bool>{};

    try {
      results['photos'] = await hasPhotosPermission();
      results['camera'] = await hasCameraPermission();

      print('🔍 权限状态检查结果: $results');
    } catch (e) {
      print('批量检查权限时发生错误: $e');
    }

    return results;
  }
}