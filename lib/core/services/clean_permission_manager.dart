import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 清洁的权限管理器
/// 专门解决新安装APP权限申请问题，确保用户能够正常授权
class CleanPermissionManager {
  static final CleanPermissionManager _instance = CleanPermissionManager._internal();
  factory CleanPermissionManager() => _instance;
  CleanPermissionManager._internal();

  /// 请求相册权限
  /// 确保新安装的APP能够正常申请权限
  Future<bool> requestPhotosPermission(BuildContext context) async {
    try {
      print('📸 开始请求相册权限...');
      
      // 1. 检查当前权限状态
      final currentStatus = await Permission.photos.status;
      print('📋 当前相册权限状态: ${currentStatus.name}');
      
      // 2. 如果已经有权限，直接返回成功
      if (currentStatus.isGranted || currentStatus.isLimited) {
        print('✅ 相册权限已授权');
        return true;
      }
      
      // 3. 如果是永久拒绝，引导用户到设置
      if (currentStatus.isPermanentlyDenied) {
        print('❌ 相册权限被永久拒绝，引导用户到设置');
        return await _handlePermanentlyDenied(context, '相册权限');
      }
      
      // 4. 首次申请或被拒绝，显示权限说明
      final shouldRequest = await _showPermissionDialog(
        context,
        '相册权限',
        '为了让您选择照片生成AI婚纱照，需要访问您的相册',
        Icons.photo_library,
        Colors.blue,
      );
      
      if (!shouldRequest) {
        print('❌ 用户拒绝授权相册权限');
        return false;
      }
      
      // 5. 请求权限
      print('🔄 正在请求相册权限...');
      final result = await Permission.photos.request();
      print('📝 相册权限请求结果: ${result.name}');
      
      // 6. 处理结果
      if (result.isGranted || result.isLimited) {
        print('✅ 相册权限申请成功');
        _showSuccessSnackBar(context, '相册权限已开启');
        return true;
      } else if (result.isPermanentlyDenied) {
        print('❌ 相册权限被永久拒绝');
        return await _handlePermanentlyDenied(context, '相册权限');
      } else {
        print('⚠️ 相册权限被拒绝');
        _showErrorSnackBar(context, '相册权限被拒绝，无法选择照片');
        return false;
      }
    } catch (e) {
      print('💥 请求相册权限时发生错误: $e');
      _showErrorSnackBar(context, '权限申请失败，请重试');
      return false;
    }
  }

  /// 请求相机权限
  Future<bool> requestCameraPermission(BuildContext context) async {
    try {
      print('📷 开始请求相机权限...');
      
      // 1. 检查当前权限状态
      final currentStatus = await Permission.camera.status;
      print('📋 当前相机权限状态: ${currentStatus.name}');
      
      // 2. 如果已经有权限，直接返回成功
      if (currentStatus.isGranted) {
        print('✅ 相机权限已授权');
        return true;
      }
      
      // 3. 如果是永久拒绝，引导用户到设置
      if (currentStatus.isPermanentlyDenied) {
        print('❌ 相机权限被永久拒绝，引导用户到设置');
        return await _handlePermanentlyDenied(context, '相机权限');
      }
      
      // 4. 首次申请或被拒绝，显示权限说明
      final shouldRequest = await _showPermissionDialog(
        context,
        '相机权限',
        '为了让您拍摄照片生成AI婚纱照，需要访问您的相机',
        Icons.camera_alt,
        Colors.green,
      );
      
      if (!shouldRequest) {
        print('❌ 用户拒绝授权相机权限');
        return false;
      }
      
      // 5. 请求权限
      print('🔄 正在请求相机权限...');
      final result = await Permission.camera.request();
      print('📝 相机权限请求结果: ${result.name}');
      
      // 6. 处理结果
      if (result.isGranted) {
        print('✅ 相机权限申请成功');
        _showSuccessSnackBar(context, '相机权限已开启');
        return true;
      } else if (result.isPermanentlyDenied) {
        print('❌ 相机权限被永久拒绝');
        return await _handlePermanentlyDenied(context, '相机权限');
      } else {
        print('⚠️ 相机权限被拒绝');
        _showErrorSnackBar(context, '相机权限被拒绝，无法拍照');
        return false;
      }
    } catch (e) {
      print('💥 请求相机权限时发生错误: $e');
      _showErrorSnackBar(context, '权限申请失败，请重试');
      return false;
    }
  }

  /// 批量请求图片相关权限
  Future<Map<String, bool>> requestImagePermissions(BuildContext context) async {
    print('🎯 开始批量请求图片相关权限...');
    
    final results = <String, bool>{};
    
    // 先请求相册权限
    results['photos'] = await requestPhotosPermission(context);
    
    // 再请求相机权限
    results['camera'] = await requestCameraPermission(context);
    
    print('📊 权限申请结果: $results');
    return results;
  }

  /// 检查权限状态
  Future<Map<String, bool>> checkPermissionStatus() async {
    try {
      final photosStatus = await Permission.photos.status;
      final cameraStatus = await Permission.camera.status;
      
      final results = {
        'photos': photosStatus.isGranted || photosStatus.isLimited,
        'camera': cameraStatus.isGranted,
      };
      
      print('🔍 当前权限状态: $results');
      return results;
    } catch (e) {
      print('💥 检查权限状态失败: $e');
      return {'photos': false, 'camera': false};
    }
  }

  /// 处理永久拒绝的权限
  Future<bool> _handlePermanentlyDenied(BuildContext context, String permissionName) async {
    final shouldOpenSettings = await _showSettingsDialog(context, permissionName);
    
    if (shouldOpenSettings) {
      await openAppSettings();
      // 注意：用户从设置返回后，我们不能自动检测权限变化
      // 需要用户手动重新尝试功能
      return false;
    }
    
    return false;
  }

  /// 显示权限申请对话框
  Future<bool> _showPermissionDialog(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
  ) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(icon, color: color, size: 24),
              const SizedBox(width: 8),
              Expanded(child: Text(title)),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(description, style: const TextStyle(fontSize: 16)),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '我们的承诺：',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text('• 只在您主动使用时访问'),
                    const Text('• 不会上传或分享您的内容'),
                    const Text('• 严格保护您的隐私'),
                    const Text('• 可随时在设置中撤销'),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('暂不开启'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: color,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('开启权限'),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示设置引导对话框
  Future<bool> _showSettingsDialog(BuildContext context, String permissionName) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.settings, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              Text('需要开启$permissionName'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('$permissionName已被拒绝，请手动开启：'),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '操作步骤：',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.orange.shade700,
                      ),
                    ),
                    const SizedBox(height: 8),
                    const Text('1. 点击"去设置"按钮'),
                    const Text('2. 找到"Hera"应用'),
                    Text('3. 开启$permissionName'),
                    const Text('4. 返回应用重新尝试'),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () => Navigator.of(context).pop(false),
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去设置'),
              onPressed: () => Navigator.of(context).pop(true),
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示成功提示
  void _showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Text(message),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 2),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }

  /// 显示错误提示
  void _showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            Expanded(child: Text(message)),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
      ),
    );
  }
}
