import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 简化的权限服务
/// 专门处理相册和相机权限申请
class SimplePermissionService {
  static final SimplePermissionService _instance = SimplePermissionService._internal();
  factory SimplePermissionService() => _instance;
  SimplePermissionService._internal();

  /// 重置并请求相册权限
  Future<bool> requestPhotosPermission(BuildContext context) async {
    try {
      print('🏞️ 开始请求相册权限');
      
      // 检查当前权限状态
      PermissionStatus status = await Permission.photos.status;
      print('📋 当前相册权限状态: ${status.name}');
      print('📋 状态详细信息: granted=${status.isGranted}, limited=${status.isLimited}, denied=${status.isDenied}, permanentlyDenied=${status.isPermanentlyDenied}');
      
      // 如果已经有权限，直接返回true
      if (status.isGranted || status.isLimited) {
        print('✅ 相册权限已获得 (${status.name})');
        return true;
      }
      
      // 如果被永久拒绝，显示特殊提示
      if (status.isPermanentlyDenied) {
        print('❌ 相册权限被永久拒绝');
        if (context.mounted) {
          await _showAppResetDialog(context, '相册权限');
        }
        return false;
      }
      
      // 显示权限说明对话框
      if (context.mounted) {
        print('💬 显示权限说明对话框');
        bool shouldRequest = await _showPermissionRequestDialog(
          context, 
          '相册访问权限', 
          '为了让您选择照片生成精美的AI婚纱照，我们需要访问您的相册。\n\n我们承诺：\n• 只在您主动选择时访问相册\n• 不会存储或分享您的照片\n• 严格保护您的隐私'
        );
        
        if (!shouldRequest) {
          print('🚫 用户取消了权限申请');
          return false;
        }
      }
      
      // 请求权限
      print('🔄 正在请求相册权限...');
      final result = await Permission.photos.request();
      print('📝 相册权限请求结果: ${result.name}');
      print('📝 结果详细信息: granted=${result.isGranted}, limited=${result.isLimited}, denied=${result.isDenied}, permanentlyDenied=${result.isPermanentlyDenied}');
      
      // 检查结果
      if (result.isGranted || result.isLimited) {
        print('✅ 相册权限申请成功');
        return true;
      } else if (result.isPermanentlyDenied && context.mounted) {
        print('❌ 相册权限被永久拒绝，需要重置应用');
        await _showAppResetDialog(context, '相册权限');
        return false;
      } else {
        print('⚠️ 相册权限申请被拒绝');
        return false;
      }
    } catch (e) {
      print('💥 请求相册权限时发生错误: $e');
      return false;
    }
  }

  /// 请求相机权限
  Future<bool> requestCameraPermission(BuildContext context) async {
    try {
      print('📷 开始请求相机权限');
      
      // 检查当前权限状态
      PermissionStatus status = await Permission.camera.status;
      print('📋 当前相机权限状态: ${status.name}');
      print('📋 状态详细信息: granted=${status.isGranted}, denied=${status.isDenied}, permanentlyDenied=${status.isPermanentlyDenied}');
      
      // 如果已经有权限，直接返回true
      if (status.isGranted) {
        print('✅ 相机权限已获得');
        return true;
      }
      
      // 如果被永久拒绝，引导用户到设置页面
      if (status.isPermanentlyDenied) {
        print('❌ 相机权限被永久拒绝');
        if (context.mounted) {
          await _showPermissionDeniedDialog(context, '相机权限');
        }
        return false;
      }
      
      // 显示权限说明对话框
      if (context.mounted) {
        print('💬 显示相机权限说明对话框');
        bool shouldRequest = await _showCameraPermissionRequestDialog(context);
        
        if (!shouldRequest) {
          print('🚫 用户取消了相机权限申请');
          return false;
        }
      }
      
      // 请求权限
      print('🔄 正在请求相机权限...');
      final result = await Permission.camera.request();
      print('📝 相机权限请求结果: ${result.name}');
      print('📝 结果详细信息: granted=${result.isGranted}, denied=${result.isDenied}, permanentlyDenied=${result.isPermanentlyDenied}');
      
      // 检查结果
      if (result.isGranted) {
        print('✅ 相机权限申请成功');
        return true;
      } else if (result.isPermanentlyDenied && context.mounted) {
        print('❌ 相机权限被永久拒绝，引导用户到设置');
        await _showPermissionDeniedDialog(context, '相机权限');
        return false;
      } else {
        print('⚠️ 相机权限申请被拒绝');
        return false;
      }
    } catch (e) {
      print('💥 请求相机权限时发生错误: $e');
      return false;
    }
  }

  /// 显示权限申请说明对话框
  Future<bool> _showPermissionRequestDialog(BuildContext context, String title, String message) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              const Icon(Icons.photo_library, color: Colors.blue, size: 24),
              const SizedBox(width: 8),
              Expanded(child: Text(title, style: const TextStyle(fontSize: 18))),
            ],
          ),
          content: Text(
            message,
            style: const TextStyle(fontSize: 16, height: 1.4),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('暂不开启', style: TextStyle(color: Colors.grey)),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('开启权限'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示相机权限申请说明对话框
  Future<bool> _showCameraPermissionRequestDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              const Icon(Icons.camera_alt, color: Colors.green, size: 24),
              const SizedBox(width: 8),
              const Expanded(child: Text('相机访问权限', style: TextStyle(fontSize: 18))),
            ],
          ),
          content: const Text(
            '为了让您拍摄照片生成精美的AI婚纱照，我们需要访问您的相机。\n\n我们承诺：\n• 只在您主动拍照时使用相机\n• 不会在后台偷拍或录制\n• 严格保护您的隐私',
            style: TextStyle(fontSize: 16, height: 1.4),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('暂不开启', style: TextStyle(color: Colors.grey)),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('开启权限'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示应用重置对话框
  Future<void> _showAppResetDialog(BuildContext context, String permissionName) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              const Icon(Icons.warning, color: Colors.red, size: 24),
              const SizedBox(width: 8),
              Text('需要重新安装应用'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text('$permissionName已被永久拒绝。为了正常使用功能，请按以下步骤操作：'),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.grey.shade100,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text('📱 步骤1: 删除应用', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.red.shade700)),
                    const Text('• 长按应用图标'),
                    const Text('• 选择"删除App"'),
                    const SizedBox(height: 8),
                    Text('🔄 步骤2: 重新安装', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.blue.shade700)),
                    const Text('• 重新运行应用'),
                    const Text('• 这样可以重置所有权限'),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('我知道了'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
          ],
        );
      },
    );
  }

  /// 显示权限被拒绝的对话框
  Future<void> _showPermissionDeniedDialog(BuildContext context, String permissionName) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              const Icon(Icons.settings, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              Text('需要开启$permissionName'),
            ],
          ),
          content: Text('$permissionName已被拒绝。为了正常使用功能，请在设置中手动开启$permissionName。'),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop();
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去设置'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
          ],
        );
      },
    );
  }

  /// 检查相册权限状态
  Future<bool> hasPhotosPermission() async {
    try {
      final status = await Permission.photos.status;
      return status.isGranted || status.isLimited;
    } catch (e) {
      print('检查相册权限时发生错误: $e');
      return false;
    }
  }

  /// 检查相机权限状态
  Future<bool> hasCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      print('检查相机权限时发生错误: $e');
      return false;
    }
  }
} 