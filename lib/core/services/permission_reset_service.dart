import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// 权限重置服务
/// 专门解决权限被永久拒绝的问题，提供重置和恢复机制
class PermissionResetService {
  static final PermissionResetService _instance = PermissionResetService._internal();
  factory PermissionResetService() => _instance;
  PermissionResetService._internal();

  /// 强制重置相册权限
  /// 这是解决永久拒绝问题的核心方法
  Future<bool> forceResetPhotosPermission(BuildContext context) async {
    try {
      print('🔄 开始强制重置相册权限...');
      
      // 第一步：检查当前状态
      final currentStatus = await Permission.photos.status;
      print('📋 当前权限状态: ${currentStatus.name}');
      
      if (currentStatus.isGranted || currentStatus.isLimited) {
        print('✅ 权限已经可用');
        return true;
      }
      
      // 第二步：如果是永久拒绝，显示重置指导
      if (currentStatus.isPermanentlyDenied) {
        print('⚠️ 检测到权限被永久拒绝，开始重置流程');
        return await _handlePermanentlyDeniedReset(context);
      }
      
      // 第三步：尝试直接请求权限
      return await _requestPermissionWithGuidance(context);
      
    } catch (e) {
      print('💥 权限重置失败: $e');
      return false;
    }
  }

  /// 处理永久拒绝的权限重置
  Future<bool> _handlePermanentlyDeniedReset(BuildContext context) async {
    // 显示重置指导对话框
    final shouldReset = await _showResetGuidanceDialog(context);
    
    if (!shouldReset) {
      return false;
    }
    
    // 引导用户到设置
    await openAppSettings();
    
    // 等待用户返回后重新检查
    return await _waitForPermissionChange(context);
  }

  /// 带指导的权限请求
  Future<bool> _requestPermissionWithGuidance(BuildContext context) async {
    // 显示权限说明
    final shouldRequest = await _showPermissionExplanationDialog(context);
    
    if (!shouldRequest) {
      return false;
    }
    
    // 请求权限
    print('🔄 正在请求相册权限...');
    final result = await Permission.photos.request();
    print('📝 权限请求结果: ${result.name}');
    
    if (result.isGranted || result.isLimited) {
      print('✅ 权限获取成功!');
      if (context.mounted) {
        _showSuccessMessage(context);
      }
      return true;
    } else if (result.isPermanentlyDenied) {
      print('❌ 权限被永久拒绝，启动重置流程');
      return await _handlePermanentlyDeniedReset(context);
    } else {
      print('⚠️ 权限被拒绝');
      if (context.mounted) {
        _showDeniedMessage(context);
      }
      return false;
    }
  }

  /// 显示重置指导对话框
  Future<bool> _showResetGuidanceDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.refresh, color: Colors.orange, size: 24),
              const SizedBox(width: 8),
              const Text('重置相册权限'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '相册权限被永久拒绝，需要手动重置。请按以下步骤操作：',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.settings, color: Colors.blue, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '重置步骤：',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text('1. 点击"去设置"按钮'),
                    const Text('2. 找到"Hera"应用'),
                    const Text('3. 点击"照片"选项'),
                    const Text('4. 选择"所有照片"或"选中的照片"'),
                    const Text('5. 返回应用即可正常使用'),
                  ],
                ),
              ),
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  children: [
                    Icon(Icons.lightbulb, color: Colors.green, size: 16),
                    const SizedBox(width: 6),
                    const Expanded(
                      child: Text(
                        '提示：选择"所有照片"可获得完整权限',
                        style: TextStyle(fontSize: 12, color: Colors.green),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.orange,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去设置'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示权限说明对话框
  Future<bool> _showPermissionExplanationDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.photo_library, color: Colors.blue, size: 24),
              const SizedBox(width: 8),
              const Text('相册访问权限'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '为了让您选择照片生成AI婚纱照，我们需要访问您的相册。',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '我们的承诺：',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.blue.shade700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text('• 只在您主动选择时访问相册'),
                    const Text('• 不会上传或分享您的照片'),
                    const Text('• 严格保护您的隐私'),
                    const Text('• 可随时在设置中撤销权限'),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('暂不开启'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.blue,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('开启权限'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 等待权限状态改变
  Future<bool> _waitForPermissionChange(BuildContext context) async {
    print('⏳ 等待用户在设置中修改权限...');
    
    // 显示等待对话框
    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text('等待权限设置...'),
              const SizedBox(height: 8),
              const Text(
                '请在设置中开启相册权限后返回',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }
    
    // 轮询检查权限状态
    for (int i = 0; i < 30; i++) { // 最多等待30秒
      await Future.delayed(const Duration(seconds: 1));
      
      final status = await Permission.photos.status;
      print('🔍 检查权限状态 (${i + 1}/30): ${status.name}');
      
      if (status.isGranted || status.isLimited) {
        print('✅ 检测到权限已开启!');
        if (context.mounted) {
          Navigator.of(context).pop(); // 关闭等待对话框
          _showSuccessMessage(context);
        }
        return true;
      }
    }
    
    print('⏰ 等待超时');
    if (context.mounted) {
      Navigator.of(context).pop(); // 关闭等待对话框
      _showTimeoutMessage(context);
    }
    return false;
  }

  /// 显示成功消息
  void _showSuccessMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('相册权限已成功开启！'),
          ],
        ),
        backgroundColor: Colors.green,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示拒绝消息
  void _showDeniedMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('相册权限被拒绝，无法选择照片'),
          ],
        ),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 显示超时消息
  void _showTimeoutMessage(BuildContext context) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.access_time, color: Colors.white, size: 20),
            const SizedBox(width: 8),
            const Text('等待超时，请手动检查权限设置'),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: const Duration(seconds: 3),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  /// 强制重置相机权限
  Future<bool> forceResetCameraPermission(BuildContext context) async {
    try {
      print('🔄 开始强制重置相机权限...');

      // 检查当前状态
      final currentStatus = await Permission.camera.status;
      print('📋 当前相机权限状态: ${currentStatus.name}');

      if (currentStatus.isGranted) {
        print('✅ 相机权限已经可用');
        return true;
      }

      // 如果是永久拒绝，显示重置指导
      if (currentStatus.isPermanentlyDenied) {
        print('⚠️ 检测到相机权限被永久拒绝，开始重置流程');
        return await _handleCameraPermanentlyDeniedReset(context);
      }

      // 尝试直接请求权限
      return await _requestCameraPermissionWithGuidance(context);

    } catch (e) {
      print('💥 相机权限重置失败: $e');
      return false;
    }
  }

  /// 处理相机权限永久拒绝的重置
  Future<bool> _handleCameraPermanentlyDeniedReset(BuildContext context) async {
    final shouldReset = await _showCameraResetGuidanceDialog(context);

    if (!shouldReset) {
      return false;
    }

    await openAppSettings();
    return await _waitForCameraPermissionChange(context);
  }

  /// 带指导的相机权限请求
  Future<bool> _requestCameraPermissionWithGuidance(BuildContext context) async {
    final shouldRequest = await _showCameraPermissionExplanationDialog(context);

    if (!shouldRequest) {
      return false;
    }

    print('🔄 正在请求相机权限...');
    final result = await Permission.camera.request();
    print('📝 相机权限请求结果: ${result.name}');

    if (result.isGranted) {
      print('✅ 相机权限获取成功!');
      if (context.mounted) {
        _showSuccessMessage(context);
      }
      return true;
    } else if (result.isPermanentlyDenied) {
      print('❌ 相机权限被永久拒绝，启动重置流程');
      return await _handleCameraPermanentlyDeniedReset(context);
    } else {
      print('⚠️ 相机权限被拒绝');
      if (context.mounted) {
        _showDeniedMessage(context);
      }
      return false;
    }
  }

  /// 显示相机权限重置指导对话框
  Future<bool> _showCameraResetGuidanceDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.refresh, color: Colors.green, size: 24),
              const SizedBox(width: 8),
              const Text('重置相机权限'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '相机权限被永久拒绝，需要手动重置。请按以下步骤操作：',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.settings, color: Colors.green, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '重置步骤：',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.green.shade700,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    const Text('1. 点击"去设置"按钮'),
                    const Text('2. 找到"Hera"应用'),
                    const Text('3. 开启"相机"权限'),
                    const Text('4. 返回应用即可正常使用'),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('取消'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('去设置'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 显示相机权限说明对话框
  Future<bool> _showCameraPermissionExplanationDialog(BuildContext context) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Icon(Icons.camera_alt, color: Colors.green, size: 24),
              const SizedBox(width: 8),
              const Text('相机访问权限'),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                '为了让您拍摄照片生成AI婚纱照，我们需要访问您的相机。',
                style: TextStyle(fontSize: 16),
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '我们的承诺：',
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        color: Colors.green.shade700,
                      ),
                    ),
                    const SizedBox(height: 4),
                    const Text('• 只在您主动拍照时使用相机'),
                    const Text('• 不会在后台偷拍或录制'),
                    const Text('• 严格保护您的隐私'),
                    const Text('• 可随时在设置中撤销权限'),
                  ],
                ),
              ),
            ],
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('暂不开启'),
              onPressed: () {
                Navigator.of(context).pop(false);
              },
            ),
            ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('开启权限'),
              onPressed: () {
                Navigator.of(context).pop(true);
              },
            ),
          ],
        );
      },
    ) ?? false;
  }

  /// 等待相机权限状态改变
  Future<bool> _waitForCameraPermissionChange(BuildContext context) async {
    print('⏳ 等待用户在设置中修改相机权限...');

    if (context.mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) => AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              const Text('等待相机权限设置...'),
              const SizedBox(height: 8),
              const Text(
                '请在设置中开启相机权限后返回',
                style: TextStyle(fontSize: 12, color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    for (int i = 0; i < 30; i++) {
      await Future.delayed(const Duration(seconds: 1));

      final status = await Permission.camera.status;
      print('🔍 检查相机权限状态 (${i + 1}/30): ${status.name}');

      if (status.isGranted) {
        print('✅ 检测到相机权限已开启!');
        if (context.mounted) {
          Navigator.of(context).pop();
          _showSuccessMessage(context);
        }
        return true;
      }
    }

    print('⏰ 等待超时');
    if (context.mounted) {
      Navigator.of(context).pop();
      _showTimeoutMessage(context);
    }
    return false;
  }

  /// 检查权限状态
  Future<bool> checkPhotosPermission() async {
    try {
      final status = await Permission.photos.status;
      return status.isGranted || status.isLimited;
    } catch (e) {
      print('检查权限状态失败: $e');
      return false;
    }
  }

  /// 检查相机权限状态
  Future<bool> checkCameraPermission() async {
    try {
      final status = await Permission.camera.status;
      return status.isGranted;
    } catch (e) {
      print('检查相机权限状态失败: $e');
      return false;
    }
  }
}
