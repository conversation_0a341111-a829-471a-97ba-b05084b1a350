import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/log_service.dart';

/// 权限类型枚举
enum RequestPermissionType {
  camera,
  photos,
  storage,
  manageExternalStorage,
  notification,
  microphone,
  location,
  locationWhenInUse,
  locationAlways,
}

/// 权限请求管理器
/// 
/// 基于最佳实践的权限处理管理器，提供链式调用和智能权限处理
class RequestPermissionManager {
  final LogService _logger = LogService();
  
  /// 权限类型
  RequestPermissionType? _permissionType;
  
  /// 权限被拒绝时的回调
  Function()? _onPermissionDenied;
  
  /// 权限被授权时的回调
  Function()? _onPermissionGranted;
  
  /// 权限被永久拒绝时的回调
  Function()? _onPermissionPermanentlyDenied;
  
  /// 权限受限时的回调
  Function()? _onPermissionRestricted;
  
  /// 有限权限时的回调（iOS 14+）
  Function()? _onPermissionLimited;
  
  /// 上下文（用于显示对话框）
  BuildContext? _context;

  /// 构造函数
  RequestPermissionManager(RequestPermissionType permissionType) {
    _permissionType = permissionType;
  }

  /// 设置上下文
  RequestPermissionManager withContext(BuildContext context) {
    _context = context;
    return this;
  }

  /// 设置权限被拒绝时的回调
  RequestPermissionManager onPermissionDenied(Function() onDenied) {
    _onPermissionDenied = onDenied;
    return this;
  }

  /// 设置权限被授权时的回调
  RequestPermissionManager onPermissionGranted(Function() onGranted) {
    _onPermissionGranted = onGranted;
    return this;
  }

  /// 设置权限被永久拒绝时的回调
  RequestPermissionManager onPermissionPermanentlyDenied(Function() onPermanentlyDenied) {
    _onPermissionPermanentlyDenied = onPermanentlyDenied;
    return this;
  }

  /// 设置权限受限时的回调
  RequestPermissionManager onPermissionRestricted(Function() onRestricted) {
    _onPermissionRestricted = onRestricted;
    return this;
  }

  /// 设置有限权限时的回调（iOS 14+）
  RequestPermissionManager onPermissionLimited(Function() onLimited) {
    _onPermissionLimited = onLimited;
    return this;
  }

  /// 获取Permission对象
  Permission _getPermissionFromType(RequestPermissionType permissionType) {
    switch (permissionType) {
      case RequestPermissionType.camera:
        return Permission.camera;
      case RequestPermissionType.photos:
        return Permission.photos;
      case RequestPermissionType.storage:
        return Permission.storage;
      case RequestPermissionType.manageExternalStorage:
        return Permission.manageExternalStorage;
      case RequestPermissionType.notification:
        return Permission.notification;
      case RequestPermissionType.microphone:
        return Permission.microphone;
      case RequestPermissionType.location:
        return Permission.location;
      case RequestPermissionType.locationWhenInUse:
        return Permission.locationWhenInUse;
      case RequestPermissionType.locationAlways:
        return Permission.locationAlways;
    }
  }

  /// 获取权限名称
  String _getPermissionName(RequestPermissionType permissionType) {
    switch (permissionType) {
      case RequestPermissionType.camera:
        return '相机';
      case RequestPermissionType.photos:
      case RequestPermissionType.storage:
        return '相册';
      case RequestPermissionType.manageExternalStorage:
        return '存储';
      case RequestPermissionType.notification:
        return '通知';
      case RequestPermissionType.microphone:
        return '麦克风';
      case RequestPermissionType.location:
      case RequestPermissionType.locationWhenInUse:
      case RequestPermissionType.locationAlways:
        return '位置';
    }
  }

  /// 智能检查权限状态
  Future<PermissionStatus> checkPermissionStatus() async {
    if (_permissionType == null) {
      throw Exception('Permission type is not set');
    }

    final permission = _getPermissionFromType(_permissionType!);
    final status = await permission.status;
    
    _logger.d('检查权限状态 ${_permissionType!.name}: ${status.name}');
    return status;
  }

  /// 显示权限说明对话框
  Future<bool> _showPermissionRationale() async {
    if (_context == null) return true;

    final permissionName = _getPermissionName(_permissionType!);
    final result = await showDialog<bool>(
      context: _context!,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.security, color: Colors.blue.shade600),
            const SizedBox(width: 8),
            Text('需要${permissionName}权限'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_getPermissionDescription()),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.blue.shade600, size: 16),
                      const SizedBox(width: 6),
                      Text(
                        '为什么需要此权限？',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 6),
                  Text(
                    _getPermissionReason(),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.blue.shade600,
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('暂不授权'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('授权'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 获取权限描述
  String _getPermissionDescription() {
    switch (_permissionType!) {
      case RequestPermissionType.camera:
        return '我们需要访问相机来拍摄照片，用于生成您的AI婚纱照。';
      case RequestPermissionType.photos:
      case RequestPermissionType.storage:
        return '我们需要访问您的相册来选择照片，用于生成AI婚纱照。';
      case RequestPermissionType.manageExternalStorage:
        return '我们需要存储权限来保存生成的婚纱照到您的设备。';
      case RequestPermissionType.notification:
        return '我们需要通知权限来及时告知您AI生成进度和完成状态。';
      case RequestPermissionType.microphone:
        return '我们需要麦克风权限用于语音交互功能。';
      case RequestPermissionType.location:
      case RequestPermissionType.locationWhenInUse:
      case RequestPermissionType.locationAlways:
        return '我们需要位置权限来提供基于位置的个性化服务。';
    }
  }

  /// 获取权限需要原因
  String _getPermissionReason() {
    switch (_permissionType!) {
      case RequestPermissionType.camera:
        return '• 拍摄高质量照片\n• 实时预览拍摄效果\n• 确保最佳图像质量';
      case RequestPermissionType.photos:
      case RequestPermissionType.storage:
        return '• 选择您最满意的照片\n• 支持多张照片处理\n• 保护隐私，仅在选择时访问';
      case RequestPermissionType.manageExternalStorage:
        return '• 保存生成的AI婚纱照\n• 管理照片存储空间\n• 便于后续查看和分享';
      case RequestPermissionType.notification:
        return '• 及时获取处理进度\n• 第一时间知道生成完成\n• 重要消息不错过';
      case RequestPermissionType.microphone:
        return '• 语音指令控制\n• 语音搜索功能\n• 更便捷的交互体验';
      case RequestPermissionType.location:
      case RequestPermissionType.locationWhenInUse:
      case RequestPermissionType.locationAlways:
        return '• 提供本地化服务\n• 个性化推荐\n• 基于位置的特色功能';
    }
  }

  /// 显示永久拒绝对话框
  Future<bool> _showPermanentlyDeniedDialog() async {
    if (_context == null) return false;

    final permissionName = _getPermissionName(_permissionType!);
    final result = await showDialog<bool>(
      context: _context!,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.settings, color: Colors.orange),
            const SizedBox(width: 8),
            const Text('需要手动开启权限'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('${permissionName}权限被永久拒绝，请按以下步骤手动开启：'),
            const SizedBox(height: 12),
            _buildStep('1', '点击"去设置"按钮'),
            _buildStep('2', '找到"权限"或"隐私"选项'),
            _buildStep('3', '开启${permissionName}权限'),
            _buildStep('4', '返回应用重新尝试'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('取消'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
            ),
            child: const Text('去设置'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  Widget _buildStep(String number, String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: const BoxDecoration(
              color: Colors.blue,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                number,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: const TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }

  /// 执行权限请求
  Future<void> execute() async {
    try {
      if (_permissionType == null) {
        throw Exception('Permission type is not set');
      }

      _logger.i('开始执行权限请求: ${_permissionType!.name}');

      // 1. 先检查当前权限状态
      final currentStatus = await checkPermissionStatus();
      
      // 2. 如果已经有权限，直接执行成功回调
      if (currentStatus.isGranted) {
        _logger.d('权限已授权，直接执行成功回调');
        _onPermissionGranted?.call();
        return;
      }
      
      // 3. iOS 14+ 有限权限也视为成功
      if (currentStatus.isLimited) {
        _logger.d('权限为有限授权（iOS），执行成功回调');
        _onPermissionLimited?.call();
        _onPermissionGranted?.call(); // 也执行成功回调
        return;
      }

      // 4. 如果权限被永久拒绝，显示设置引导
      if (currentStatus.isPermanentlyDenied) {
        _logger.w('权限被永久拒绝，显示设置引导');
        if (_context != null) {
          final shouldOpenSettings = await _showPermanentlyDeniedDialog();
          if (shouldOpenSettings) {
            await openAppSettings();
          }
        }
        _onPermissionPermanentlyDenied?.call();
        return;
      }

      // 5. 如果权限受限，执行受限回调
      if (currentStatus.isRestricted) {
        _logger.w('权限受限制');
        _onPermissionRestricted?.call();
        return;
      }

      // 6. 显示权限说明对话框
      if (_context != null) {
        final shouldRequest = await _showPermissionRationale();
        if (!shouldRequest) {
          _logger.d('用户拒绝授权权限');
          _onPermissionDenied?.call();
          return;
        }
      }

      // 7. 请求权限
      final permission = _getPermissionFromType(_permissionType!);
      final status = await permission.request();
      
      _logger.i('权限请求结果 ${_permissionType!.name}: ${status.name}');

      // 8. 处理权限请求结果
      if (status.isGranted) {
        _onPermissionGranted?.call();
      } else if (status.isLimited) {
        _onPermissionLimited?.call();
        _onPermissionGranted?.call(); // 也执行成功回调
      } else if (status.isDenied) {
        _onPermissionDenied?.call();
      } else if (status.isPermanentlyDenied) {
        _onPermissionPermanentlyDenied?.call();
      } else if (status.isRestricted) {
        _onPermissionRestricted?.call();
      }

    } catch (e, stackTrace) {
      _logger.e('权限请求执行失败', e, stackTrace);
      _onPermissionDenied?.call();
    }
  }
} 