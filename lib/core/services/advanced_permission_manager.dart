import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/log_service.dart';
import 'request_permission_manager.dart';

/// 权限组合类型
enum PermissionGroup {
  essential,      // 基础权限组（必需的）
  imageProcessing, // 图像处理权限组
  enhanced,       // 增强功能权限组
  optional,       // 可选权限组
}

/// 高级权限管理器
/// 
/// 提供智能的权限管理策略，支持批量权限请求、权限组管理和自适应权限策略
class AdvancedPermissionManager {
  static final AdvancedPermissionManager _instance = AdvancedPermissionManager._internal();
  factory AdvancedPermissionManager() => _instance;
  AdvancedPermissionManager._internal();

  final LogService _logger = LogService();
  
  /// 检查Context是否仍然有效
  bool mounted(BuildContext context) {
    return context.mounted;
  }
  
  /// 权限组配置
  static const Map<PermissionGroup, List<RequestPermissionType>> _permissionGroups = {
    PermissionGroup.essential: [
      RequestPermissionType.notification,
    ],
    PermissionGroup.imageProcessing: [
      RequestPermissionType.camera,
      RequestPermissionType.photos,
    ],
    PermissionGroup.enhanced: [
      RequestPermissionType.storage,
      RequestPermissionType.manageExternalStorage,
    ],
    PermissionGroup.optional: [
      RequestPermissionType.microphone,
      RequestPermissionType.location,
    ],
  };

  /// 根据使用场景请求图片相关权限
  Future<Map<RequestPermissionType, bool>> requestImagePermissions(
    BuildContext context, {
    bool includeCamera = true,
    bool includePhotos = true,
    bool includeStorage = false,
  }) async {
    _logger.i('开始请求图片相关权限组');
    
    final results = <RequestPermissionType, bool>{};
    final permissions = <RequestPermissionType>[];
    
    if (includeCamera) permissions.add(RequestPermissionType.camera);
    if (includePhotos) permissions.add(RequestPermissionType.photos);
    if (includeStorage) {
      if (Platform.isAndroid) {
        permissions.add(RequestPermissionType.storage);
        // Android 11+ 可能需要管理外部存储权限
        if (Platform.operatingSystemVersion.contains('API level 30') ||
            Platform.operatingSystemVersion.contains('API level 31') ||
            Platform.operatingSystemVersion.contains('API level 32') ||
            Platform.operatingSystemVersion.contains('API level 33') ||
            Platform.operatingSystemVersion.contains('API level 34')) {
          permissions.add(RequestPermissionType.manageExternalStorage);
        }
      }
    }
    
         // 显示权限组说明
     if (!mounted(context)) return results;
     
     final shouldProceed = await _showPermissionGroupDialog(
       context,
       '图片处理权限',
       '为了让您能够拍照和选择照片来生成AI婚纱照，我们需要以下权限：',
       permissions,
     );
    
    if (!shouldProceed) {
      _logger.d('用户拒绝权限组授权');
      for (final permission in permissions) {
        results[permission] = false;
      }
      return results;
    }
    
    // 逐个请求权限
    for (final permission in permissions) {
      final hasPermission = await _requestSinglePermission(context, permission);
      results[permission] = hasPermission;
      
             // 如果是关键权限被拒绝，可以提供替代方案
       if (!hasPermission && _isCriticalPermission(permission)) {
         _logger.w('关键权限 ${permission.name} 被拒绝');
         if (mounted(context)) {
           await _showCriticalPermissionFailedDialog(context, permission);
         }
       }
    }
    
    _logger.i('图片权限组请求完成: $results');
    return results;
  }

  /// 智能请求相册权限（根据Android版本优化）
  Future<bool> requestPhotosPermissionSmart(BuildContext context) async {
    _logger.i('智能请求相册权限');
    
    if (Platform.isIOS) {
      return await _requestSinglePermission(context, RequestPermissionType.photos);
    } else {
      // Android：优先尝试photos权限（Android 13+推荐）
      final photosResult = await _requestSinglePermission(context, RequestPermissionType.photos);
      
      if (photosResult) {
        _logger.d('Android photos权限授权成功');
        return true;
      }
      
      // 如果photos权限失败，尝试storage权限（兼容性处理）
      _logger.d('photos权限失败，尝试storage权限');
      final storageResult = await _requestSinglePermission(context, RequestPermissionType.storage);
      
      if (storageResult) {
        _logger.d('Android storage权限授权成功');
        return true;
      }
      
      _logger.w('Android 相册权限全部失败');
      return false;
    }
  }

  /// 批量检查权限状态
  Future<Map<RequestPermissionType, bool>> checkPermissionStatuses(
    List<RequestPermissionType> permissions,
  ) async {
    final results = <RequestPermissionType, bool>{};
    
    for (final permission in permissions) {
      try {
        final status = await RequestPermissionManager(permission).checkPermissionStatus();
        results[permission] = status == PermissionStatus.granted || status == PermissionStatus.limited;
      } catch (e) {
        _logger.e('检查权限状态失败: ${permission.name}', e);
        results[permission] = false;
      }
    }
    
    return results;
  }

  /// 请求权限组
  Future<Map<RequestPermissionType, bool>> requestPermissionGroup(
    BuildContext context,
    PermissionGroup group, {
    bool showGroupDialog = true,
  }) async {
    final permissions = _permissionGroups[group] ?? [];
    if (permissions.isEmpty) {
      _logger.w('权限组 ${group.name} 为空');
      return {};
    }
    
    _logger.i('请求权限组: ${group.name}');
    
         if (showGroupDialog) {
       if (!mounted(context)) return {for (final p in permissions) p: false};
       
       final shouldProceed = await _showPermissionGroupDialog(
         context,
         _getGroupTitle(group),
         _getGroupDescription(group),
         permissions,
       );
      
      if (!shouldProceed) {
        _logger.d('用户拒绝权限组 ${group.name}');
        return {for (final p in permissions) p: false};
      }
    }
    
    final results = <RequestPermissionType, bool>{};
    for (final permission in permissions) {
      results[permission] = await _requestSinglePermission(context, permission);
    }
    
    return results;
  }

  /// 显示权限组说明对话框
  Future<bool> _showPermissionGroupDialog(
    BuildContext context,
    String title,
    String description,
    List<RequestPermissionType> permissions,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.security, color: Colors.blue.shade600),
            const SizedBox(width: 8),
            Expanded(child: Text(title)),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(description),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.list_alt, color: Colors.blue.shade600, size: 16),
                      const SizedBox(width: 6),
                      Text(
                        '需要的权限：',
                        style: TextStyle(
                          fontWeight: FontWeight.w600,
                          color: Colors.blue.shade700,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  ...permissions.map((permission) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Icon(
                          _getPermissionIcon(permission),
                          size: 16,
                          color: Colors.blue.shade600,
                        ),
                        const SizedBox(width: 8),
                        Text(
                          _getPermissionDisplayName(permission),
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.blue.shade600,
                          ),
                        ),
                      ],
                    ),
                  )),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('暂不授权'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('开始授权'),
          ),
        ],
      ),
    );

    return result ?? false;
  }

  /// 显示关键权限失败对话框
  Future<void> _showCriticalPermissionFailedDialog(
    BuildContext context,
    RequestPermissionType permission,
  ) async {
    await showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Row(
          children: [
            Icon(Icons.warning, color: Colors.orange),
            const SizedBox(width: 8),
            const Text('重要权限被拒绝'),
          ],
        ),
        content: Text(
          '${_getPermissionDisplayName(permission)}权限对于应用的核心功能非常重要。\n\n'
          '没有此权限，部分功能可能无法正常使用。您可以稍后在设置中开启此权限。',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('我知道了'),
          ),
        ],
      ),
    );
  }

  /// 请求单个权限
  Future<bool> _requestSinglePermission(
    BuildContext context,
    RequestPermissionType permission,
  ) async {
    bool hasPermission = false;
    
    await RequestPermissionManager(permission)
        .withContext(context)
        .onPermissionGranted(() => hasPermission = true)
        .onPermissionLimited(() => hasPermission = true)
        .onPermissionDenied(() => hasPermission = false)
        .onPermissionPermanentlyDenied(() => hasPermission = false)
        .onPermissionRestricted(() => hasPermission = false)
        .execute();
    
    return hasPermission;
  }

  /// 判断是否为关键权限
  bool _isCriticalPermission(RequestPermissionType permission) {
    return [
      RequestPermissionType.camera,
      RequestPermissionType.photos,
    ].contains(permission);
  }

  /// 获取权限组标题
  String _getGroupTitle(PermissionGroup group) {
    switch (group) {
      case PermissionGroup.essential:
        return '基础权限';
      case PermissionGroup.imageProcessing:
        return '图片处理权限';
      case PermissionGroup.enhanced:
        return '增强功能权限';
      case PermissionGroup.optional:
        return '可选权限';
    }
  }

  /// 获取权限组描述
  String _getGroupDescription(PermissionGroup group) {
    switch (group) {
      case PermissionGroup.essential:
        return '这些是应用正常运行所需的基础权限。';
      case PermissionGroup.imageProcessing:
        return '为了让您能够拍照和选择照片，我们需要以下权限：';
      case PermissionGroup.enhanced:
        return '这些权限将为您提供更好的存储和管理体验：';
      case PermissionGroup.optional:
        return '这些可选权限可以提供更丰富的功能体验：';
    }
  }

  /// 获取权限显示名称
  String _getPermissionDisplayName(RequestPermissionType permission) {
    switch (permission) {
      case RequestPermissionType.camera:
        return '相机权限';
      case RequestPermissionType.photos:
        return '相册权限';
      case RequestPermissionType.storage:
        return '存储权限';
      case RequestPermissionType.manageExternalStorage:
        return '管理存储权限';
      case RequestPermissionType.notification:
        return '通知权限';
      case RequestPermissionType.microphone:
        return '麦克风权限';
      case RequestPermissionType.location:
      case RequestPermissionType.locationWhenInUse:
      case RequestPermissionType.locationAlways:
        return '位置权限';
    }
  }

  /// 获取权限图标
  IconData _getPermissionIcon(RequestPermissionType permission) {
    switch (permission) {
      case RequestPermissionType.camera:
        return Icons.camera_alt;
      case RequestPermissionType.photos:
      case RequestPermissionType.storage:
      case RequestPermissionType.manageExternalStorage:
        return Icons.photo_library;
      case RequestPermissionType.notification:
        return Icons.notifications;
      case RequestPermissionType.microphone:
        return Icons.mic;
      case RequestPermissionType.location:
      case RequestPermissionType.locationWhenInUse:
      case RequestPermissionType.locationAlways:
        return Icons.location_on;
    }
  }
} 