import 'dart:io';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import '../utils/log_service.dart';

/// 智能权限管理器
/// 
/// 简化版权限管理器，专注于核心功能和稳定性
class SmartPermissionManager {
  static final SmartPermissionManager _instance = SmartPermissionManager._internal();
  factory SmartPermissionManager() => _instance;
  SmartPermissionManager._internal();

  final LogService _logger = LogService();

  /// 智能请求相册权限
  /// 
  /// 根据平台和版本自动选择最合适的权限类型
  Future<bool> requestPhotosPermissionSmart(BuildContext context) async {
    try {
      _logger.i('开始智能相册权限申请');
      
      if (Platform.isIOS) {
        return await _requestIOSPhotosPermission(context);
      } else {
        return await _requestAndroidPhotosPermission(context);
      }
    } catch (e) {
      _logger.e('智能相册权限申请失败', e);
      return false;
    }
  }

  /// iOS 相册权限申请
  Future<bool> _requestIOSPhotosPermission(BuildContext context) async {
    final permission = Permission.photos;
    final status = await permission.status;
    
    _logger.d('iOS 相册权限当前状态: ${status.name}');
    
    // 已有权限直接返回
    if (status.isGranted || status.isLimited) {
      _logger.i('iOS 相册权限已获得');
      return true;
    }
    
    // 永久拒绝引导设置
    if (status.isPermanentlyDenied) {
      _logger.w('iOS 相册权限被永久拒绝');
      if (context.mounted) {
        await _showSettingsDialog(context, '相册权限');
      }
      return false;
    }
    
    // 显示权限说明并请求
    if (context.mounted) {
      final shouldRequest = await _showPermissionDialog(
        context,
        '相册权限',
        '为了让您选择照片生成AI婚纱照，需要访问您的相册。',
        Icons.photo_library,
      );
      
      if (!shouldRequest) {
        _logger.i('用户拒绝了相册权限申请');
        return false;
      }
    }
    
    // 请求权限
    final result = await permission.request();
    _logger.i('iOS 相册权限申请结果: ${result.name}');
    
    return result.isGranted || result.isLimited;
  }

  /// Android 相册权限申请
  Future<bool> _requestAndroidPhotosPermission(BuildContext context) async {
    _logger.d('开始 Android 相册权限申请');
    
    // 优先尝试 photos 权限（Android 13+）
    bool hasPhotosPermission = await _tryRequestPermission(Permission.photos);
    
    if (hasPhotosPermission) {
      _logger.i('Android photos 权限获得成功');
      return true;
    }
    
    // 兼容性处理：尝试 storage 权限
    _logger.d('photos 权限失败，尝试 storage 权限');
    
    if (context.mounted) {
      final shouldTryStorage = await _showPermissionDialog(
        context,
        '存储权限',
        '为了访问您的照片，需要存储权限。这是为了兼容您的设备版本。',
        Icons.storage,
      );
      
      if (!shouldTryStorage) {
        return false;
      }
    }
    
    bool hasStoragePermission = await _tryRequestPermission(Permission.storage);
    
    if (hasStoragePermission) {
      _logger.i('Android storage 权限获得成功');
      return true;
    }
    
    _logger.w('Android 相册权限申请失败');
    return false;
  }

  /// 尝试请求单个权限
  Future<bool> _tryRequestPermission(Permission permission) async {
    try {
      final status = await permission.status;
      
      // 已有权限
      if (status.isGranted || status.isLimited) {
        return true;
      }
      
      // 永久拒绝
      if (status.isPermanentlyDenied) {
        return false;
      }
      
      // 请求权限
      final result = await permission.request();
      return result.isGranted || result.isLimited;
    } catch (e) {
      _logger.e('权限请求失败: ${permission.toString()}', e);
      return false;
    }
  }

  /// 智能请求相机权限
  Future<bool> requestCameraPermissionSmart(BuildContext context) async {
    try {
      _logger.i('开始智能相机权限申请');
      
      final permission = Permission.camera;
      final status = await permission.status;
      
      _logger.d('相机权限当前状态: ${status.name}');
      
      // 已有权限
      if (status.isGranted) {
        _logger.i('相机权限已获得');
        return true;
      }
      
      // 永久拒绝
      if (status.isPermanentlyDenied) {
        _logger.w('相机权限被永久拒绝');
        if (context.mounted) {
          await _showSettingsDialog(context, '相机权限');
        }
        return false;
      }
      
      // 显示权限说明并请求
      if (context.mounted) {
        final shouldRequest = await _showPermissionDialog(
          context,
          '相机权限',
          '为了让您拍摄照片生成AI婚纱照，需要访问您的相机。',
          Icons.camera_alt,
        );
        
        if (!shouldRequest) {
          _logger.i('用户拒绝了相机权限申请');
          return false;
        }
      }
      
      // 请求权限
      final result = await permission.request();
      _logger.i('相机权限申请结果: ${result.name}');
      
      return result.isGranted;
    } catch (e) {
      _logger.e('智能相机权限申请失败', e);
      return false;
    }
  }

  /// 智能请求通知权限
  Future<bool> requestNotificationPermissionSmart(BuildContext context) async {
    try {
      _logger.i('开始智能通知权限申请');
      
      final permission = Permission.notification;
      final status = await permission.status;
      
      _logger.d('通知权限当前状态: ${status.name}');
      
      // 已有权限
      if (status.isGranted) {
        _logger.i('通知权限已获得');
        return true;
      }
      
      // 永久拒绝（通知权限不强制要求）
      if (status.isPermanentlyDenied) {
        _logger.w('通知权限被永久拒绝，但不影响主要功能');
        return false;
      }
      
      // 显示权限说明并请求
      if (context.mounted) {
        final shouldRequest = await _showPermissionDialog(
          context,
          '通知权限',
          '开启通知权限，我们将及时通知您处理进度和结果。',
          Icons.notifications,
          isOptional: true,
        );
        
        if (!shouldRequest) {
          _logger.i('用户跳过了通知权限申请');
          return false;
        }
      }
      
      // 请求权限
      final result = await permission.request();
      _logger.i('通知权限申请结果: ${result.name}');
      
      return result.isGranted;
    } catch (e) {
      _logger.e('智能通知权限申请失败', e);
      return false;
    }
  }

  /// 显示权限申请对话框
  Future<bool> _showPermissionDialog(
    BuildContext context,
    String permissionName,
    String description,
    IconData icon, {
    bool isOptional = false,
  }) async {
    return await showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(icon, color: Colors.blue.shade600, size: 28),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '需要$permissionName',
                style: const TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              description,
              style: const TextStyle(fontSize: 16, height: 1.5),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.blue.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.blue.shade200),
              ),
              child: Row(
                children: [
                  Icon(Icons.security, color: Colors.blue.shade600, size: 20),
                  const SizedBox(width: 8),
                  const Expanded(
                    child: Text(
                      '我们承诺保护您的隐私，仅在必要时使用权限',
                      style: TextStyle(fontSize: 14, color: Colors.black87),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          if (isOptional)
            TextButton(
              onPressed: () => Navigator.of(context).pop(false),
              child: const Text('暂时跳过'),
            ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(isOptional ? '不开启' : '拒绝'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.blue.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('立即授权'),
          ),
        ],
      ),
    ) ?? false;
  }

  /// 显示设置引导对话框
  Future<void> _showSettingsDialog(BuildContext context, String permissionName) async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(Icons.settings, color: Colors.orange.shade600, size: 28),
            const SizedBox(width: 12),
            const Expanded(
              child: Text(
                '需要在设置中开启权限',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '$permissionName已被拒绝，请在设置中手动开启。',
              style: const TextStyle(fontSize: 16, height: 1.5),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(Icons.info_outline, color: Colors.orange.shade600, size: 20),
                      const SizedBox(width: 8),
                      const Text(
                        '操作步骤：',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '1. 点击"前往设置"\n2. 找到并点击权限管理\n3. 开启对应权限\n4. 返回应用重试',
                    style: TextStyle(fontSize: 14, height: 1.4),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('稍后设置'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await openAppSettings();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange.shade600,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('前往设置'),
          ),
        ],
      ),
    );
  }

  /// 批量检查权限状态
  Future<Map<String, bool>> checkPermissionStatuses() async {
    final results = <String, bool>{};
    
    try {
      // 检查相册权限
      if (Platform.isIOS) {
        final photosStatus = await Permission.photos.status;
        results['photos'] = photosStatus.isGranted || photosStatus.isLimited;
      } else {
        final photosStatus = await Permission.photos.status;
        final storageStatus = await Permission.storage.status;
        results['photos'] = photosStatus.isGranted || storageStatus.isGranted;
      }
      
      // 检查相机权限
      final cameraStatus = await Permission.camera.status;
      results['camera'] = cameraStatus.isGranted;
      
      // 检查通知权限
      final notificationStatus = await Permission.notification.status;
      results['notification'] = notificationStatus.isGranted;
      
      _logger.d('权限状态检查结果: $results');
    } catch (e) {
      _logger.e('权限状态检查失败', e);
    }
    
    return results;
  }

  /// 显示权限状态摘要
  void showPermissionSummary(Map<String, bool> statuses) {
    final granted = statuses.values.where((status) => status).length;
    final total = statuses.length;
    
    _logger.i('权限状态摘要: $granted/$total 个权限已获得');
    
    statuses.forEach((permission, granted) {
      final status = granted ? '✅ 已授权' : '❌ 未授权';
      _logger.d('$permission: $status');
    });
  }
} 