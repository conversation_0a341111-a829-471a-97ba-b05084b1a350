<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Hera</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>hera</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<!-- 相机权限描述 -->
	<key>NSCameraUsageDescription</key>
	<string>Hera需要访问您的相机来拍摄照片，用于生成精美的AI婚纱照。我们不会存储或分享您的相机数据。</string>

	<!-- 相册访问权限描述 -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Hera需要访问您的相册来选择照片，用于生成个性化的AI婚纱照。我们只在您主动选择时访问相册，并严格保护您的隐私。</string>

	<!-- 相册保存权限描述 -->
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>Hera需要将生成的精美婚纱照保存到您的相册中。我们只会保存您确认的照片，不会访问您的其他照片。</string>

	<!-- iOS 14+ 限制访问权限描述 -->
	<key>NSPhotoLibraryLimitedUsageDescription</key>
	<string>Hera需要访问您选择的照片来生成AI婚纱照。您可以选择允许访问所有照片或仅选中的照片。</string>

	<!-- 防止自动弹出限制访问提示，让应用自己控制权限流程 -->
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
</dict>
</plist>
