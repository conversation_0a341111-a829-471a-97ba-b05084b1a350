# Miscellaneous
*.class
*.log
*.pyc
*.swp
.DS_Store
.atom/
.build/
.buildlog/
.history
.svn/
.swiftpm/
migrate_working_dir/

# IntelliJ related
*.iml
*.ipr
*.iws
.idea/

# VS Code related
.vscode/
*.code-workspace

# Flutter/Dart/Pub related
**/doc/api/
**/ios/Flutter/.last_build_id
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.pub-cache/
.pub/
/build/
*.g.dart
*.freezed.dart
*.gr.dart
*.config.dart

# Symbolication related
app.*.symbols

# Obfuscation related
app.*.map.json

# Android Studio will place build artifacts here
/android/app/debug
/android/app/profile
/android/app/release

# Fastlane related
**/fastlane/report.xml
**/fastlane/Preview.html
**/fastlane/screenshots
**/fastlane/test_output

# Coverage reports
coverage/
*.lcov

# Local environment files
.env
.env.local
*.env.*

# Mason related
.mason/

# Native build artifacts
**/Flutter/ephemeral/
**/Flutter/Flutter.podspec
**/Flutter/Generated.xcconfig
**/Flutter/App.framework/
**/Flutter/Flutter.framework/
**/Flutter/flutter_assets/
