import 'package:flutter_test/flutter_test.dart';
import 'package:hera/core/services/smart_permission_manager.dart';

void main() {
  group('SmartPermissionManager Tests', () {
    late SmartPermissionManager permissionManager;

    setUp(() {
      permissionManager = SmartPermissionManager();
    });

    test('SmartPermissionManager should be a singleton', () {
      final instance1 = SmartPermissionManager();
      final instance2 = SmartPermissionManager();
      
      expect(instance1, equals(instance2));
    });

    test('checkPermissionStatuses should return a map', () async {
      final statuses = await permissionManager.checkPermissionStatuses();
      
      expect(statuses, isA<Map<String, bool>>());
      expect(statuses.containsKey('photos'), isTrue);
      expect(statuses.containsKey('camera'), isTrue);
      expect(statuses.containsKey('notification'), isTrue);
    });

    test('showPermissionSummary should not throw', () {
      final testStatuses = {
        'photos': true,
        'camera': false,
        'notification': true,
      };
      
      expect(() => permissionManager.showPermissionSummary(testStatuses), 
             returnsNormally);
    });
  });
} 